{"name": "4913", "version": "0.2.3", "private": true, "scripts": {"prepare": "husky", "start": "mcs-scripts start -p 8081", "start:native": "react-native start --reset-cache", "lint": "eslint ./src --fix"}, "dependencies": {"@cvn/rn-panel-kit": "0.5.1", "@cvn/rne-slider": "1.0.0", "@cvn/react-native-echarts-wrapper": "0.0.1-1", "@cvn/react-native-sms-verifycode": "0.0.1", "@cvn/react-native-orientation": "3.1.3-6", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/blur": "4.4.0", "@react-native-clipboard/clipboard": "1.14.1", "@react-native-community/cameraroll": "4.1.2", "@react-native-community/geolocation": "3.0.5", "@react-native-community/segmented-control": "2.2.2", "@react-navigation/bottom-tabs": "6.0.9", "@react-navigation/native": "6.1.17", "@react-navigation/stack": "6.3.29", "cvn-panel-kit": "1.0.3-49", "dayjs": "1.11.11", "echarts": "5.5.1", "events": "1.1.1", "lodash-es": "4.17.21", "mobx": "4.3.1", "mobx-react": "5.2.8", "react": "18.2.0", "react-native": "0.72.15", "react-native-background-timer": "2.4.1", "react-native-canvas": "0.1.38", "react-native-device-info": "11.1.0", "react-native-elements": "3.4.3", "react-native-fast-image": "8.6.3", "react-native-fs": "2.20.0", "react-native-gesture-handler": "2.17.0", "react-native-linear-gradient": "2.8.3", "react-native-maps": "1.15.4", "react-native-modal": "13.0.1", "react-native-permissions": "4.1.5", "react-native-picker-scrollview": "1.0.1", "react-native-reanimated": "3.8.1", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.32.0", "react-native-svg": "15.3.0", "react-native-vector-icons": "10.1.0", "react-native-webview": "13.10.3", "retry": "0.13.1", "style-equal": "1.0.0"}, "devDependencies": {"@babel/core": "7.24.7", "@babel/eslint-parser": "7.18.9", "@babel/eslint-plugin": "7.18.10", "@babel/plugin-proposal-decorators": "7.17.9", "@babel/preset-env": "7.24.7", "@babel/preset-react": "7.18.6", "@babel/runtime": "7.24.7", "@commitlint/cli": "19.3.0", "@commitlint/config-conventional": "19.2.2", "@cvn/metro-code-split": "0.1.9", "@react-native/eslint-config": "0.72.2", "@react-native/metro-config": "0.72.12", "@testing-library/react-native": "12.1.2", "@tsconfig/react-native": "3.0.5", "babel-jest": "29.7.0", "babel-plugin-import": "1.13.5", "babel-plugin-module-resolver": "4.1.0", "babel-plugin-transform-decorators-legacy": "1.3.5", "babel-plugin-transform-remove-console": "6.9.4", "commitizen": "4.3.0", "csv-writer": "1.6.0", "cz-customizable": "6.3.0", "cz-git": "1.9.3", "eslint": "8.25.0", "eslint-config-chervon": "1.0.2", "eslint-import-resolver-alias": "1.1.2", "eslint-import-resolver-react-native": "0.2.0", "eslint-plugin-ft-flow": "3.0.7", "eslint-plugin-react": "7.31.1", "husky": "9.0.11", "jest": "29.7.0", "metro-react-native-babel-preset": "0.76.9", "prettier": "2.7.1", "prop-types": "15.8.1", "react-native-bundle-visualizer": "3.1.1", "react-test-renderer": "18.2.0", "rimraf": "3.0.2", "typescript": "4.9.5"}, "engines": {"node": ">=16"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}}