{
    "compilerOptions": {
        "experimentalDecorators": true,
        "module": "commonjs",
        "target": "ES6",
        "baseUrl": ".",
        "paths": {
        "IOTRN/*": ["./*"],
        "@tracer/*": ["./src/utils/tracer/*"],

        "@components/*": ["./src/components/*"],
        "@api/*": ["./src/api/*"],
        "@config/*": ["./src/config/*"],
        "@i18n/*": ["./src/i18n/*"],
        "@assets/*": ["./src/assets/*"],
        "@utils/*": ["./src/utils/*"],
        "@pages/*": ["./src/pages/*"],
        "@feature/*": ["./src/feature/*"],
        "@cvn-icon/*": ["./src/utils/cvn-icon/*"],

        "@components": ["./src/components"],
        "@api": ["./src/api"],
        "@config": ["./src/config"],
        "@i18n": ["./src/i18n"],
        "@assets": ["./src/assets"],
        "@utils": ["./src/utils"],
        "@pages": ["./src/pages"],
        "@feature": ["./src/feature"],
        "@cvn-icon": ["./src/utils/cvn-icon"],
        "@tracer": ["./src/utils/tracer"],
      }
    },
    "exclude": [
      "node_modules",
      "**/node_modules/*"
    ]

}
