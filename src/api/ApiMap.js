/*
 * @Author: <EMAIL>
 * @Date: 2023-11-22 16:50:41
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2024-01-25 15:21:40
 * @FilePath: /Z6/src/api/ApiMap.js
 * @Description: api统一管理
 *
 */
// const dev = 'https://dev.na.chervoncloud.com/app';
// const sit = 'https://sit.na.chervoncloud.com/app';
// const sit = 'https://pre.na.chervoncloud.com/app';
const sit = '';
const apiHost = __DEV__ ? sit : '';

const device = {
  deviceDetail: '/device/detail',
  deviceUnbind: '/device/unbind',
  editProperty: '/app/robotmower/editProperty',
  deviceEdit: '/device/edit',
  getPartsOverview: '/device/parts/overview', // 配件入口数据
};

const set = {};

const allApiMap = {
  ...device,
  ...set,
};

const apiWithhostMap = {};
Object.keys(allApiMap).forEach(key => {
  apiWithhostMap[key] = `${apiHost}${allApiMap[key]}`;
});

export default {
  ...apiWithhostMap,
};
