/*
 * @Author: <EMAIL>
 * @Date: 2023-11-22 16:12:28
 * @LastEditors: cdlsp
 * @FilePath: /chervonreactnative/Z6/src/utils/cmd/index.js
 * @Description:  命令相关工具类
 *
 */
import {Utils} from 'cvn-panel-kit';
import StringUtil from 'cvn-panel-kit/src/utils/string';
import {crc16xmodem} from 'cvn-panel-kit/src/utils/crc16';
import {z6Device} from '@feature/Z6NativeApi';
import {
  panelModeStores as panelStore,
  panelModeActions as actionMap,
} from 'src/mobx/index.js';
import {CMD_DICTIONARY} from 'src/utils/home';
const {NumberUtils} = Utils;
const HEADER = '55aa';
const VERSION = '00'; // 版本号
const INDEX = '00';
const PACKAGE_NUM = '00'; // 包序号
const CMD_SET = 'e5'; // 参数下发
const CMD_REAL = 'd0'; // 实时数据
const CMD_VERIFY = 'e2'; // 鉴权
const CMD_SET_LENGTH = '1a'; // 参数下发数据长度26

/**
 * @description:获取deviceId对应的hexString
 */
const getHexDeviceId = () => {
  const {panel} = panelStore;
  const {deviceId, hexStringDeviceId} = panel;
  let result = '';
  if (typeof hexStringDeviceId === 'string' && hexStringDeviceId.length > 0) {
    return hexStringDeviceId;
  }
  if (deviceId) {
    result = encodeDeviceId(deviceId);
  }
  return result || '';
};

/**
 * @description:设备回复命令解析
 * @param {String} cmdHex
 */
const parseIotData = (cmdHex = '', callBack = () => {}) => {
  const lowCmdHex = cmdHex.toLowerCase();
  const checkHead = lowCmdHex.substring(0, 4);
  if (checkHead !== HEADER) {
    return;
  }
  const cmdType = lowCmdHex.substring(6, 6 + 2);
  switch (cmdType) {
    // 实时
    case CMD_REAL:
      parseRealTimeIotResponseCmd(cmdHex, callBack);
      break;
    // 命令设置后的回复
    case CMD_SET:
      parseParamSetResponseCmd(cmdHex, callBack);
      break;
    // 原生发起鉴权后的回复
    case CMD_VERIFY:
      parseVerifyResponseCmd(cmdHex, callBack);
      break;
    default:
      break;
  }
};

/**
 * @description: 解析下发命令设置后的回复数据
 * @param {String} cmdHex e.g：55AA0FE5000300000000000000FBFC
 * @param {Function} callBack
 */
const parseParamSetResponseCmd = (cmdHex = '', callBack = () => {}) => {
  if (cmdHex.length === 30) {
    queryRealTimeCmd();
  }
  callBack('');
};

/**
 * @description: 解析下原生鉴权后的回复数据
 * @param {String} cmdHex e.g：55AA0FE5000300000000000000FBFC
 * @param {Function} callBack
 */
const parseVerifyResponseCmd = (cmdHex = '', callBack = () => {}) => {
  if (cmdHex.length !== 50) {
    return;
  }
  const hexDeviceIdString = findHexDataAtByteIndex(cmdHex, 6, 10);
  if (
    typeof hexDeviceIdString === 'string' &&
    hexDeviceIdString.length === 20
  ) {
    actionMap.panelActions.setHexStringDeviceId(hexDeviceIdString);
  }
  callBack('');
};

/**
 * @description: 解析实时数据
 * @param {String} cmdHex
 * @param {Function} callBack
 */
const parseRealTimeIotResponseCmd = (cmdHex = '', callBack = () => {}) => {
  let toastMessage = '';
  if (cmdHex.length !== 266) {
    return;
  }
  /**
   * @description:基本状态1
   */
  const baseStatus1 = findHexDataAtByteIndex(cmdHex, 19, 1);
  /**
   * @description:基本状态2
   */
  const baseStatus2 = findHexDataAtByteIndex(cmdHex, 20, 1);
  /**
   * @description:电池仓仓位状态
   */
  const batteryBin = findHexDataAtByteIndex(cmdHex, 21, 1);
  /**
   * @description:电池仓报警状态
   */
  const batteryBinWarning = findHexDataAtByteIndex(cmdHex, 23, 1);
  /**
   * @description:电池仓各仓位过温状态
   */
  const batteryBinOverTemp = findHexDataAtByteIndex(cmdHex, 24, 1);
  /**
   * @description:电池仓各仓位欠压状态
   */
  const batteryBinUnderVol = findHexDataAtByteIndex(cmdHex, 25, 1);
  /**
   * @description:电池仓各仓位充电状态
   */
  const batteryBinCharging = findHexDataAtByteIndex(cmdHex, 26, 1);
  /**
   * @description:当前设置状态
   */
  const currentSet = findHexDataAtByteIndex(cmdHex, 27, 1);
  /**
   * @description:电量显示百分比
   */
  const batteryPercent = findHexDataAtByteIndex(cmdHex, 67, 1);
  /**
   * @description:电池包1电量百分比
   */
  const batteryPack1Percent = findHexDataAtByteIndex(cmdHex, 68, 1);
  /**
   * @description:电池包2电量百分比
   */
  const batteryPack2Percent = findHexDataAtByteIndex(cmdHex, 69, 1);
  /**
   * @description:电池包3电量百分比
   */
  const batteryPack3Percent = findHexDataAtByteIndex(cmdHex, 70, 1);
  /**
   * @description:电池包4电量百分比
   */
  const batteryPack4Percent = findHexDataAtByteIndex(cmdHex, 71, 1);
  /**
   * @description:电池包5电量百分比
   */
  const batteryPack5Percent = findHexDataAtByteIndex(cmdHex, 72, 1);
  /**
   * @description:电池包6电量百分比
   */
  const batteryPack6Percent = findHexDataAtByteIndex(cmdHex, 73, 1);
  /**
   * @description:设定的行驶速度档位
   */
  const setedSpeedGear = findHexDataAtByteIndex(cmdHex, 86);
  /**
   * @description:设定的刀片转速档位
   */
  const setedBladeRpmGear = findHexDataAtByteIndex(cmdHex, 87);
  /**
   * @description:灯光延迟熄灭时间
   */
  const lightDelayOffTime = findHexDataAtByteIndex(cmdHex, 88);
  /**
   * @description:大灯亮度档位值
   */
  const lightBrightnessGear = findHexDataAtByteIndex(cmdHex, 89);
  /**
   * @description:直线行走矫正数值
   */
  const lineRunCorrect = findHexDataAtByteIndex(cmdHex, 90);
  /**
   * @description:割草电机转速
   */
  const rmpValue = NumberUtils.dataConversion(
    findHexDataAtByteIndex(cmdHex, 95, 2),
  );
  /**
   * @description:左自走电机正转转速
   */
  const leftMotorForwardSpeed = NumberUtils.dataConversion(
    findHexDataAtByteIndex(cmdHex, 97, 2),
  );
  /**
   * @description:总剩余放电时间
   */
  const totalRemainDisChargeTime = NumberUtils.dataConversion(
    findHexDataAtByteIndex(cmdHex, 115, 4),
  );
  /**
   * @description:总剩余充电时间
   */
  const totalRemainChargeTime = NumberUtils.dataConversion(
    findHexDataAtByteIndex(cmdHex, 119, 4),
  );

  /**
   * @description: realTimeA0 实时数据A0区域
   */
  const realTimeA0 = {
    currentSet,
  };
  /**
   * @description: realTimeA1 实时数据A1区域
   */
  const realTimeA1 = {
    setedBladeRpmGear,
    setedSpeedGear,
    lightDelayOffTime,
    lightBrightnessGear,
    lineRunCorrect,
  };
  /**
   * @description: 基本状态1
   */
  const binaryBaseStatus1 = StringUtil.hexStringToBinString(baseStatus1);
  /**
   * @description: 蓝牙连接状态
   */
  const bleConnected = parseA0ValueAtBitIndex(binaryBaseStatus1, 6);
  /**
   * @description: 充电状态 1为正在充电
   */
  const isCharging = parseA0ValueAtBitIndex(binaryBaseStatus1, 0);
  /**
   * @description: 锁定状态 1为锁定
   */
  const isLock = parseA0ValueAtBitIndex(binaryBaseStatus1, 4);

  const binaryBaseStatus2 = StringUtil.hexStringToBinString(baseStatus2); // 基本状态2
  /**
   * @description: 是否割草中 1为割草中 来自power+ app源码
   */
  const isMowing = parseA0ValueAtBitIndex(binaryBaseStatus2, 0);
  // 当前设置状态
  const binaryCurrentSet = StringUtil.hexStringToBinString(currentSet);
  /**
   * @description: 倒车灯开关 1为打开
   */
  const isSetBackLightOn = parseA0ValueAtBitIndex(binaryCurrentSet, 0);
  /**
   * @description: 日间行车灯开关 1为打开
   */
  const isSetDayLightOn = parseA0ValueAtBitIndex(binaryCurrentSet, 1);
  /**
   * @description: 跑马灯开关 1为打开
   */
  const isSetHorseLightOn = parseA0ValueAtBitIndex(binaryCurrentSet, 2);
  /**
   * @description: 大灯亮度档位
   */
  const numLightBrightnessGear = parseInt(lightBrightnessGear, 16);
  // // 电池仓位状态
  const binaryBatteryBin = StringUtil.hexStringToBinString(batteryBin);

  // 电池仓位状态，解析成hexString，0x00、0x11
  const hexDidHaveBatteryInBin1 = parseA0Value2HexAtBitIndex(
    binaryBatteryBin,
    0,
  );
  const hexDidHaveBatteryInBin2 = parseA0Value2HexAtBitIndex(
    binaryBatteryBin,
    1,
  );
  const hexDidHaveBatteryInBin3 = parseA0Value2HexAtBitIndex(
    binaryBatteryBin,
    2,
  );
  const hexDidHaveBatteryInBin4 = parseA0Value2HexAtBitIndex(
    binaryBatteryBin,
    3,
  );
  const hexDidHaveBatteryInBin5 = parseA0Value2HexAtBitIndex(
    binaryBatteryBin,
    4,
  );
  const hexDidHaveBatteryInBin6 = parseA0Value2HexAtBitIndex(
    binaryBatteryBin,
    5,
  );

  // 电池仓各仓位过温状态
  const binaryBatteryBinOverTemp =
    StringUtil.hexStringToBinString(batteryBinOverTemp);
  const didBatteryOverTempInBin1 = parseA0ValueAtBitIndex(
    binaryBatteryBinOverTemp,
    0,
  ); // 仓位1 1为过温
  const didBatteryOverTempInBin2 = parseA0ValueAtBitIndex(
    binaryBatteryBinOverTemp,
    1,
  ); // 仓位2 1为过温
  const didBatteryOverTempInBin3 = parseA0ValueAtBitIndex(
    binaryBatteryBinOverTemp,
    2,
  ); // 仓位3 1为过温
  const didBatteryOverTempInBin4 = parseA0ValueAtBitIndex(
    binaryBatteryBinOverTemp,
    3,
  ); // 仓位4 1为过温
  const didBatteryOverTempInBin5 = parseA0ValueAtBitIndex(
    binaryBatteryBinOverTemp,
    4,
  ); // 仓位5 1为过温
  const didBatteryOverTempInBin6 = parseA0ValueAtBitIndex(
    binaryBatteryBinOverTemp,
    5,
  ); // 仓位6 1为过温

  // 电池仓各仓位欠压/充电报错/其它错状态
  const binaryBatteryBinUnderVol =
    StringUtil.hexStringToBinString(batteryBinUnderVol);
  const didBatteryUnderVolInBin1 = parseA0ValueAtBitIndex(
    binaryBatteryBinUnderVol,
    0,
  );
  const didBatteryUnderVolInBin2 = parseA0ValueAtBitIndex(
    binaryBatteryBinUnderVol,
    1,
  );
  const didBatteryUnderVolInBin3 = parseA0ValueAtBitIndex(
    binaryBatteryBinUnderVol,
    2,
  );
  const didBatteryUnderVolInBin4 = parseA0ValueAtBitIndex(
    binaryBatteryBinUnderVol,
    3,
  );
  const didBatteryUnderVolInBin5 = parseA0ValueAtBitIndex(
    binaryBatteryBinUnderVol,
    4,
  );
  const didBatteryUnderVolInBin6 = parseA0ValueAtBitIndex(
    binaryBatteryBinUnderVol,
    5,
  );

  // 电池仓各仓位充电状态
  const binaryBatteryBinCharging =
    StringUtil.hexStringToBinString(batteryBinCharging);
  const hexIsBatteryChargingInBin1 = parseA0Value2HexAtBitIndex(
    binaryBatteryBinCharging,
    0,
  );
  const hexIsBatteryChargingInBin2 = parseA0Value2HexAtBitIndex(
    binaryBatteryBinCharging,
    1,
  );
  const hexIsBatteryChargingInBin3 = parseA0Value2HexAtBitIndex(
    binaryBatteryBinCharging,
    2,
  );
  const hexIsBatteryChargingInBin4 = parseA0Value2HexAtBitIndex(
    binaryBatteryBinCharging,
    3,
  );
  const hexIsBatteryChargingInBin5 = parseA0Value2HexAtBitIndex(
    binaryBatteryBinCharging,
    4,
  );
  const hexIsBatteryChargingInBin6 = parseA0Value2HexAtBitIndex(
    binaryBatteryBinCharging,
    5,
  );

  // 组装成 原来电池仓的物模型，然后复用之前代码
  const battery_pack = `${hexDidHaveBatteryInBin1}${batteryPack1Percent}${hexDidHaveBatteryInBin2}${batteryPack2Percent}${hexDidHaveBatteryInBin3}${batteryPack3Percent}${hexDidHaveBatteryInBin4}${batteryPack4Percent}${hexDidHaveBatteryInBin5}${batteryPack5Percent}${hexDidHaveBatteryInBin6}${batteryPack6Percent}`;
  // 上面batteryBinOverTemp、batteryBinUnderVol 两个变量 每一个仓 对应的值大于0，则有故障 再与 batteryBinWarning联合得出结果,疑问，向设备端确定

  // 补充逻辑：当时充电状态时，只要bit0不能放电为1时候，不是故障
  const isErrorOut = isCharging
    ? Number.parseInt(batteryBinWarning, 16) > 1
    : Number.parseInt(batteryBinWarning, 16) > 0;

  const hexIsErrorInBin1 =
    isErrorOut || didBatteryOverTempInBin1 || didBatteryUnderVolInBin1
      ? '01'
      : '00';
  const hexIsErrorInBin2 =
    isErrorOut || didBatteryOverTempInBin2 || didBatteryUnderVolInBin2
      ? '01'
      : '00';
  const hexIsErrorInBin3 =
    isErrorOut || didBatteryOverTempInBin3 || didBatteryUnderVolInBin3
      ? '01'
      : '00';
  const hexIsErrorInBin4 =
    isErrorOut || didBatteryOverTempInBin4 || didBatteryUnderVolInBin4
      ? '01'
      : '00';
  const hexIsErrorInBin5 =
    isErrorOut || didBatteryOverTempInBin5 || didBatteryUnderVolInBin5
      ? '01'
      : '00';
  const hexIsErrorInBin6 =
    isErrorOut || didBatteryOverTempInBin6 || didBatteryUnderVolInBin6
      ? '01'
      : '00';

  const battery_basic_status = `${hexDidHaveBatteryInBin1}${hexIsErrorInBin1}${hexDidHaveBatteryInBin2}${hexIsErrorInBin2}${hexDidHaveBatteryInBin3}${hexIsErrorInBin3}${hexDidHaveBatteryInBin4}${hexIsErrorInBin4}${hexDidHaveBatteryInBin5}${hexIsErrorInBin5}${hexDidHaveBatteryInBin6}${hexIsErrorInBin6}`;
  const battery_charge_status = `${hexDidHaveBatteryInBin1}${hexIsBatteryChargingInBin1}${hexDidHaveBatteryInBin2}${hexIsBatteryChargingInBin2}${hexDidHaveBatteryInBin3}${hexIsBatteryChargingInBin3}${hexDidHaveBatteryInBin4}${hexIsBatteryChargingInBin4}${hexDidHaveBatteryInBin5}${hexIsBatteryChargingInBin5}${hexDidHaveBatteryInBin6}${hexIsBatteryChargingInBin6}`;

  /**
   * @description: 速度
   */
  const numCurrentSpeedGear = Number.parseInt(leftMotorForwardSpeed, 16);
  /**
   * @description: 割草电机的转速
   */
  const numCurrentBladeRpmGear = Number.parseInt(rmpValue, 16);
  /**
   * @description: 剩余充电时间
   */
  const numTotalRemainChargeTime = Number.parseInt(totalRemainChargeTime, 16);
  /**
   * @description: 剩余工作时间
   */
  const numTotalRemainDisChargeTime = Number.parseInt(
    totalRemainDisChargeTime,
    16,
  );
  const numberBatteryValue = Number.parseInt(batteryPercent, 16); // 这是使用百分比电量
  const numberLightDelayOffLevel = Number.parseInt(lightDelayOffTime, 16) || 0; // 灯光延迟关闭档位
  const parseResult = {
    bleConnected,
    isCharging,
    isMowing,
    numCurrentSpeedGear,
    numCurrentBladeRpmGear,
    numTotalRemainChargeTime,
    numTotalRemainDisChargeTime,
  };

  const batteryParseResult = {
    battery_pack,
    battery_basic_status,
    battery_charge_status,
    numberBatteryValue,
  };

  const backUpDataSource = {
    isSetBackLightOn,
    numLightBrightnessGear,
  };

  const dayLightDataSource = {
    isSetHorseLightOn,
    isSetDayLightOn,
  };

  const isLockDataSource = isLock;
  const result = {
    realTimeA0,
    realTimeA1,
    parseResult,
    backUpDataSource,
    dayLightDataSource,
    numberLightDelayOffLevel,
    isLockDataSource,
    batteryParseResult,
  };
  actionMap.panelActions.setRealTimeData(result);
  callBack(toastMessage);
};

/**
 * @description: 拼接并发起实时数据蓝牙请求 ios使用
 * @see: http://wiki.chervon.com.cn/pages/viewpage.action?pageId=22826056 实时解析条目
 */
const queryRealTimeCmd = () => {
  const {panel} = panelStore;
  const {mac} = panel;
  const hexStartTime = '000000';
  const hexEndTime = '000000';
  const hexDeviceId = getHexDeviceId();
  const tmpHex = `${HEADER}-19-e4-${VERSION}-${INDEX}-${hexDeviceId}-${CMD_REAL}-${hexStartTime}-${hexEndTime}`;
  const leftHex = tmpHex.split('-').join('');
  const crcHex = crc16xmodem(leftHex);
  const cmdHex = `${leftHex}${crcHex}`.toUpperCase();
  z6Device.sendZ6Cmd(cmdHex, mac || '');
};

/**
 * @description: 拼接实时数据的蓝牙请求命令 android使用 （安卓平台中，轮询使用安卓原生子线程发起）
 * @see: http://wiki.chervon.com.cn/pages/viewpage.action?pageId=22826056 实时解析条目
 */
const queryRealTimeCmdString = () => {
  const hexStartTime = '000000';
  const hexEndTime = '000000';
  const hexDeviceId = getHexDeviceId();
  const tmpHex = `${HEADER}-19-e4-${VERSION}-${INDEX}-${hexDeviceId}-${CMD_REAL}-${hexStartTime}-${hexEndTime}`;
  const leftHex = tmpHex.split('-').join('');
  const crcHex = crc16xmodem(leftHex);
  const cmdHex = `${leftHex}${crcHex}`.toUpperCase();
  return cmdHex;
};

/**
 * @description:APP下发参数设置指令
 * @param {CMD_DICTIONARY.key} actionType
 */
const appSendSetCmd = actionType => {
  const {panel} = panelStore;
  const {realTimeA0, realTimeA1, mac} = panel;
  // 设备ID position(6-15)
  const str_hexDeviceId = getHexDeviceId();
  // 开关状态 position(16) bit7～bit0
  let str_bite_switch = realTimeA0.currentSet;
  if (str_bite_switch === undefined) {
    return;
  }
  let binaryCurrentSet = StringUtil.hexStringToBinString(str_bite_switch);
  // 设定不同档位的刀片转速 position(17)
  const str_blade_speed = realTimeA1.setedBladeRpmGear;
  // 设定不同档位的行驶速度 position(18)
  const str_travel_speed = realTimeA1.setedSpeedGear;
  // 灯光延时熄灭设定时间 position(19)
  let str_light_delay = realTimeA1.lightDelayOffTime;
  // 灯光亮度设定档位 position(20)
  let str_light_brightness = realTimeA1.lightBrightnessGear;
  // 直线行走矫正 position(21)
  const str_walk_line = realTimeA1.lineRunCorrect;
  // 清除数据 0x00:无操作； 0x01：清除除SD外的数据； 0x02：清除所有数据；  position(22)
  const str_clear_data = '00';
  // 恢复默认设置 00 无操作; position(23)
  const str_reset_default = '00';
  switch (actionType) {
    case CMD_DICTIONARY.SET_UNLOCK:
      binaryCurrentSet = change8BinaryAP(binaryCurrentSet, 3, false);
      break;
    case CMD_DICTIONARY.SET_BACKUP_OFF:
      binaryCurrentSet = change8BinaryAP(binaryCurrentSet, 0, false);
      str_light_brightness = '00';
      break;
    case CMD_DICTIONARY.SET_BACKUP_BEEP:
      binaryCurrentSet = change8BinaryAP(binaryCurrentSet, 0, true);
      str_light_brightness = '01';
      break;
    case CMD_DICTIONARY.SET_BACKUP_LIGHT:
      binaryCurrentSet = change8BinaryAP(binaryCurrentSet, 0, true);
      str_light_brightness = '00';
      break;
    case CMD_DICTIONARY.SET_DAYLIGHT_OFF:
      binaryCurrentSet = change8BinaryAP(binaryCurrentSet, 1, false);
      binaryCurrentSet = change8BinaryAP(binaryCurrentSet, 2, false);
      break;
    case CMD_DICTIONARY.SET_DAYLIGHT_RUNNING:
      binaryCurrentSet = change8BinaryAP(binaryCurrentSet, 1, true);
      binaryCurrentSet = change8BinaryAP(binaryCurrentSet, 2, true);
      break;
    case CMD_DICTIONARY.SET_DAYLIGHT_ON:
      binaryCurrentSet = change8BinaryAP(binaryCurrentSet, 1, true);
      binaryCurrentSet = change8BinaryAP(binaryCurrentSet, 2, false);
      break;
    case CMD_DICTIONARY.SET_DELAY_OFF_FIRST:
      str_light_delay = '00';
      break;
    case CMD_DICTIONARY.SET_DELAY_OFF_SECOND:
      str_light_delay = '01';
      break;
    case CMD_DICTIONARY.SET_DELAY_OFF_THREE:
      str_light_delay = '02';
      break;
    case CMD_DICTIONARY.SET_DELAY_OFF_FOUR:
      str_light_delay = '03';
      break;
    case CMD_DICTIONARY.SET_DELAY_OFF_FIVE:
      str_light_delay = '04';
      break;
    case CMD_DICTIONARY.SET_DELAY_OFF_SIX:
      str_light_delay = '05';
      break;
    default:
      return;
  }

  if (binaryCurrentSet === undefined) {
    return;
  }
  let finalCurrentSet = parseInt(binaryCurrentSet, 2).toString(16);
  finalCurrentSet =
    finalCurrentSet.length === 1 ? '0' + finalCurrentSet : finalCurrentSet;

  // 拼接逻辑对应wiki：http://wiki.chervon.com.cn/pages/viewpage.action?pageId=22826056 的设置命令拼接 条目
  const tmpHex = `${HEADER}-${CMD_SET_LENGTH}-${CMD_SET}-${VERSION}-${PACKAGE_NUM}-${str_hexDeviceId}-${finalCurrentSet}-${str_blade_speed}-${str_travel_speed}-${str_light_delay}-${str_light_brightness}-${str_walk_line}-${str_clear_data}-${str_reset_default}`;
  const mactemp = typeof mac === 'undefined' ? '' : mac;
  const leftHex = tmpHex.split('-').join('');
  const crcHex = crc16xmodem(leftHex);
  const cmdHex = `${leftHex}${crcHex}`.toUpperCase();
  z6Device.sendZ6Cmd(cmdHex, mactemp);
};

/**
 * @description:解析成hexString 0x00、0x01, bit0是最右边
 * @param {String} binaryString
 *  */
export const parseA0Value2HexAtBitIndex = (binaryString = '', index = 0) => {
  const reversedString = binaryString.split('').reverse().join('');
  return `0${reversedString.substring(index, index + 1)}`;
};

/**
 * @description 开关量A0解析出值，true | false bit0是最右边的最低位
 * @param {String} binaryString
 * @param {Number} index
 * @returns true | false
 */
const parseA0ValueAtBitIndex = (binaryString = '', index = 0) => {
  const reversedString = binaryString.split('').reverse().join('');
  return Boolean(Number(reversedString.substring(index, index + 1)));
};

/**
 * @description 修改长度为8的二进制字符串的指定位置 change8BinaryAppointedPosition
 * binaryString 二进制字符串
 * index 指定位置，从右往左数 bit7～bit0
 * value true为‘1’ false为‘0’
 * */
const change8BinaryAP = (binaryString, index, value) => {
  if (typeof binaryString === 'string' && binaryString.length === 8) {
    const binaryArray = binaryString.split('');
    binaryArray[7 - index] = value ? '1' : '0';
    return binaryArray.join('');
  } else {
    return undefined;
  }
};

/**
 * @param {String} cmdHex
 * @param {Number} byteIndex
 * @param {Number} byteLen
 * @returns 指定index对应的字符串
 */
const findHexDataAtByteIndex = (cmdHex = '', byteIndex = 0, byteLen = 1) => {
  return cmdHex.substring(byteIndex * 2, byteIndex * 2 + byteLen * 2) || '00'; // 00占位一个字节
};

/**
 * @description 去除字符串头部0，转16进制，头部加0
 * @param {string} pStr
 * @param {number} length
 * @returns {string} result
 * */
const handleStrHeadTohex = (pStr, length = 2) => {
  let result = pStr.replace(/^0+/, '');
  if (result.length > 0) {
    result = parseInt(result, 10).toString(16);
  } else {
    result = '';
  }
  return result.padStart(length, '0');
};

/**
 * @description Z6正常字符的deviceId转化为16进制字符
 * @param {string} dString
 * @returns {string} result
 * */
const encodeDeviceId = dString => {
  if (typeof dString === 'string' && dString.length === 15) {
    let a1 = dString.substring(0, 1).charCodeAt(0).toString(16);
    let a2 = dString.substring(1, 2).charCodeAt(0).toString(16);
    let a3 = dString.substring(2, 3).charCodeAt(0).toString(16);
    let a4 = handleStrHeadTohex(dString.substring(3, 5));
    let a5 = handleStrHeadTohex(dString.substring(5, 7));
    let a6 = handleStrHeadTohex(dString.substring(7, 9));
    let a9 = handleStrHeadTohex(dString.substring(9, 14), 6);
    let a10 = dString.substring(14).charCodeAt(0).toString(16);
    const result = a1 + a2 + a3 + a4 + a5 + a6 + a9 + a10;
    return result;
  } else {
    return undefined;
  }
};

export {queryRealTimeCmd, queryRealTimeCmdString, appSendSetCmd, parseIotData};
