/*
 * @Author: <EMAIL>
 * @Date: 2024-01-30 19:54:35
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2024-01-31 11:18:33
 * @FilePath: /Z6/src/utils/home/<USER>
 * @Description: home页面工具类
 *
 */
import Strings from '@i18n';

/**
 * @description: HOME页倒车报警视图数据数组
 */
export const BACK_UP_ALERT_ARRAY = [
  {
    title: Strings.getLang('rn_4913_panelhome_off_textview_text'),
    cmd: 0,
  },
  {
    title: Strings.getLang('rn_4913_panelhome_beep_textview_text'),
    cmd: 1,
  },
  {
    title: Strings.getLang('rn_4913_panelhome_light_textview_text'),
    cmd: 2,
  },
];

/**
 * @description: HOME页日光灯视图数据数组
 */
export const DAYTIME_LIGHT_ARRAY = [
  {
    title: Strings.getLang('rn_4913_panelhome_off_textview_text'),
    cmd: 0,
  },
  {
    title: Strings.getLang('rn_4913_panelhome_running_textview_text'),
    cmd: 1,
  },
  {
    title: Strings.getLang('rn_4913_panelhome_on_textview_text'),
    cmd: 2,
  },
];

/**
 * @description: 发送命令字典
 */
export const CMD_DICTIONARY = {
  /**
   * @description: 解锁操作
   */
  SET_UNLOCK: 'setUnLock',
  /**
   * @description: 倒车报警default用于拼接
   */
  SET_BACKUP: 'setBackUp',
  /**
   * @description: 倒车报警OFF
   */
  SET_BACKUP_OFF: 'setBackUp0',
  /**
   * @description: 倒车报警BEEP
   */
  SET_BACKUP_BEEP: 'setBackUp1',
  /**
   * @description: 倒车报警Light
   */
  SET_BACKUP_LIGHT: 'setBackUp2',

  /**
   * @description: 日光灯default用于拼接
   */
  SET_DAYLIGHT: 'setDayLight',

  /**
   * @description: 日光灯OFF
   */
  SET_DAYLIGHT_OFF: 'setDayLight0',

  /**
   * @description: 日光灯跑马灯
   */
  SET_DAYLIGHT_RUNNING: 'setDayLight1',

  /**
   * @description: 日光灯ON
   */
  SET_DAYLIGHT_ON: 'setDayLight2',
  /**
   * @description: 大灯延迟关闭default用于拼接
   */
  SET_DELAY_OFF: 'setLightOff',
  /**
   * @description: 大灯延迟关闭1档 0秒
   */
  SET_DELAY_OFF_FIRST: 'setLightOff0',
  /**
   * @description: 大灯延迟关闭2档 5秒
   */
  SET_DELAY_OFF_SECOND: 'setLightOff1',

  /**
   * @description: 大灯延迟关闭3档 15秒
   */
  SET_DELAY_OFF_THREE: 'setLightOff2',

  /**
   * @description: 大灯延迟关闭4档 30秒
   */
  SET_DELAY_OFF_FOUR: 'setLightOff3',
  /**
   * @description: 大灯延迟关闭5档 100秒
   */
  SET_DELAY_OFF_FIVE: 'setLightOff4',
  /**
   * @description: 大灯延迟关闭5档 300秒
   */
  SET_DELAY_OFF_SIX: 'setLightOff5',
};

/**
 * @description: 设备状态字典
 */
export const CURRENT_STATUS = {
  /**
   * @description: 开启&行驶中
   */
  RUNNING: 'running',
  /**
   * @description: 充电中
   */
  CHARGING: 'charging',
  /**
   * @description: 割草中
   */
  MOWING: 'mowing',
};

/**
 * @description: 面板操作字典
 */
export const PANEL_ACTION = {
  /**
   * @description: 倒车报警
   */
  ALERT: 'alert',
  /**
   * @description: 日光灯
   */
  LIGHT: 'light',
  /**
   * @description: 延迟关灯
   */
  DELAY: 'delay',
};
