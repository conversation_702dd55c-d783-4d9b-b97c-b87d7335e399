/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-02-09 11:35:31
 * @LastEditors: cdlsp
 * @LastEditTime: 2024-03-29 16:51:04
 * @FilePath: /chervonreactnative/Z6/src/utils/topic.js
 * @Description:
 *

 */
const topicMap = {
  connect4G: '$aws/events/presence/connected/',
  disConnect4G: '$aws/events/presence/disconnected/',

  preFix: '$aws/things/',
  // 物模型查询
  shadowGet: '/shadow/get',
  // 物模型topic
  shadowUpdateAcceptedSuffix: '/shadow/update/accepted',
  // 物模型 单次拉取
  shadowGetAcceptedSuffix: '/shadow/get/accepted',
  // 上报属性
  shadowUpdateSuffix: '/shadow/update',

  preFixWithoutSymbol: 'aws/things/', // 少个s  9.8日下午，丽娜提交代码，需要马力下载后，改为things
  pathSuffix: '/path',
  // ota check
  otaSuffix: '/ota/check',
  // ota 接收
  otaAcceptedSuffix: '/ota/check/accepted',
  // jobs notify
  jobsNotifySuffix: '/jobs/notify',
};
export default topicMap;
