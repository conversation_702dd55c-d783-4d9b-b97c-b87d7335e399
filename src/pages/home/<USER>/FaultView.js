/*
 * @Author: cdlsp
 * @Date: 2023-11-23 17:15:05
 * @LastEditors: cdlsp
 * @LastEditTime: 2024-04-17 16:06:24
 * @FilePath: /chervonreactnative/Z6/src/pages/panel/components/FaultView.js
 * @Description: 故障提示组件
 *
 */
import {StyleSheet} from 'react-native';
import React, {memo} from 'react';
import {inject} from 'mobx-react/native';
import {Utils} from 'cvn-panel-kit';
import {ErrorView} from './ErrorView';
import PropTypes from 'prop-types';
import isEqual from 'lodash-es/isEqual';
import tracker, {PANEL_HOME_PAGE_ELEMENTS_MAP} from '@tracking/index';
const {JumpUtils} = Utils;

/**
 * @description: 故障信息组件
 */
export const FaultView = inject(store => ({
  panel: store.panel,
  errorText: store.panel.errorText,
  deviceMessageType: store.panel.deviceMessageType,
}))(({errorText, panel, deviceMessageType}) => {
  /**
   * 获取故障图标
   */
  const getIconSource = messageType => {
    switch (messageType) {
      case 'error':
        return require('@assets/z6/z6_icon_error.png');
      case 'warn':
      case 'warning':
        return require('@assets/z6/z6_icon_warning.png');
      case 'info':
        return require('@assets/z6/z6_icon_info.png');
      default:
        return require('@assets/z6/z6_icon_error.png');
    }
  };
  return errorText.length > 0 ? (
    <ErrorView
      text={errorText}
      style={styles.updateView}
      source={getIconSource(deviceMessageType)}
      onPress={() => {
        tracker.click({
          elementId: PANEL_HOME_PAGE_ELEMENTS_MAP.faultMessageButtonClick.id,
        });
        const {messageDataArray} = panel;
        if (messageDataArray.length === 0) {
          return;
        }
        let messageData = messageDataArray[0];
        const {uuid, messageType, deviceId, productId, createTime} =
          messageData;
        const route = 'ChervonIot://EGO/MessageCenter/MessageDetail';
        const params = {
          messageType,
          uuid,
          deviceId,
          productId,
          createTime,
        };
        JumpUtils.jumpTo(route, params);
      }}
    />
  ) : null;
});

FaultView.propTypes = {
  style: PropTypes.object,
  errorText: PropTypes.string,
  panel: PropTypes.object,
  deviceMessageType: PropTypes.string,
};

const styles = StyleSheet.create({
  fault: {
    marginBottom: 13,
  },
});

export default memo(FaultView, isEqual);
