/*
 * @Author: <EMAIL>
 * @Date: 2023-11-21 18:04:02
 * @LastEditors: cdlsp
 * @LastEditTime: 2024-05-07 16:56:32
 * @FilePath: /chervonreactnative/Z6/src/pages/panel/components/BatteryView.js
 * @Description: 电池仓组件
 *
 */
import React from 'react';
import {StyleSheet, View} from 'react-native';
import {inject} from 'mobx-react/native';
import {CVNIcon, Utils} from 'cvn-panel-kit';
import {DEVICE_WIDTH} from 'src/utils/device';
import {BatteryIconView} from './BatteryIconView';
import {map} from 'lodash-es';
import PropTypes from 'prop-types';
const {RatioUtils} = Utils;
const MCVNIcon = React.memo(CVNIcon);

/**
 * @description: 电池仓组件
 */
export const BatteryView = inject(store => ({
  data: store.panel.batteryDetailArray,
}))(({data = []}) => {
  return (
    <MCVNIcon
      style={styles.backIcon}
      resizeMode="stretch"
      source={require('@assets/z6/z6_bg_battery_store.png')}>
      {map(data, (item, index) => {
        const cStyle = index === 0 ? {} : styles.margintop;
        return (
          <View style={[styles.horizontalView, cStyle]} key={item?.key}>
            <BatteryIconView
              key={`first${index}${item?.key}`}
              style={styles.battery}
              item={item?.first}
              index={index}
            />
            <BatteryIconView
              key={`second${index}${item?.key}`}
              style={styles.battery}
              item={item?.second}
              index={index}
            />
          </View>
        );
      })}
    </MCVNIcon>
  );
});

BatteryView.propTypes = {
  data: PropTypes.array,
};

const styles = StyleSheet.create({
  // 水平等比例，竖直方向写死高度，padding
  backIcon: {
    marginTop: 12,
    marginHorizontal: RatioUtils.convertX(10.5),
    width: DEVICE_WIDTH - 81,
    height: 379,
    paddingHorizontal: RatioUtils.convertX(11),
    paddingTop: 28,
    paddingBottom: 32.5,
  },
  battery: {
    height: 89.64,
    width: RatioUtils.convertX(130),
  },
  horizontalView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  margintop: {
    marginTop: 25,
  },
  innerPadding: {
    paddingHorizontal: 15,
  },
});

export default React.memo(BatteryView);
