/*
 * @Author: <EMAIL>
 * @Date: 2023-11-22 18:16:48
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2024-05-24 11:50:34
 * @FilePath: /Z6/src/pages/panel/components/CardList.js
 * @Description: 附件、注册模块横向组件
 *
 */
import {StyleSheet, Text, View} from 'react-native';
import React, {useMemo, memo} from 'react';
import {inject} from 'mobx-react/native';
import {Utils} from 'cvn-panel-kit';
import RectView from '@pages/home/<USER>/RectView';
import Strings from '@i18n';
import PropTypes from 'prop-types';
import tracker, {PANEL_HOME_PAGE_ELEMENTS_MAP} from '@tracking/index';
const {JumpUtils} = Utils;
const registeredStatus = '1'; // 已经注册

/**
 * @description: 附件、注册模块横向组件
 */
export const CardList = inject(store => ({
  panel: store.panel,
  productId: store.panel.productId,
  detail: store.panel.deviceDetail,
  accessories: store.panel.accessories,
  deviceId: store.panel.deviceId,
  region: store.panel.region,
}))(({productId, detail, accessories, deviceId, region}) => {
  const registered = useMemo(
    () => String(detail?.infoStatus) === registeredStatus,
    [detail?.infoStatus],
  );
  /**
   * "number":10,//页面显示的数量
   * "type":1,//显示类型 1 Available 2 Required
   *  获取展示文案
   * */
  const getInfoshow = () => {
    if (accessories?.number) {
      if (accessories?.type === 1) {
        return (
          <Text style={styles.active}>
            {accessories.number}{' '}
            {Strings.getLang('rn_4913_panelhome_available_textview_text')}
          </Text>
        );
      } else if (accessories?.type === 2) {
        return (
          <Text style={styles.maintenance}>
            {accessories.number}{' '}
            {Strings.getLang('rn_4913_panelhome_required_textview_text')}
          </Text>
        );
      }
    }
    return null;
  };

  /* 附件、注册 */
  return (
    <View style={[styles.rects, styles.rectsTop]}>
      <RectView
        disabled={false}
        type="blade"
        icon={require('@assets/z6/z6_icon_accessories_card.png')}
        title={Strings.getLang('rn_common_detaillist_parts_textview_text')}
        extra={getInfoshow()}
        onPress={() => {
          tracker.click({
            elementId: PANEL_HOME_PAGE_ELEMENTS_MAP.accessoriesButtonClick.id,
          });
          // 调起native设备附件页面
          JumpUtils.jumpTo('ChervonIot://EGO/DeviceManage/productFittings', {
            deviceId: deviceId,
            productId: productId,
          });
        }}
      />
      <RectView
        type="registration"
        icon={
          registered
            ? require('@assets/z6/z6_icon_registered.png')
            : require('@assets/z6/z6_icon_register.png')
        }
        title={
          registered
            ? Strings.getLang('rn_4913_panelhome_registration_textview_text')
            : Strings.getLang('rn_4913_panelhome_registernow_textview_text')
        }
        isLast
        onPress={() => {
          tracker.click({
            elementId:
              PANEL_HOME_PAGE_ELEMENTS_MAP.deviceRegistrationButtonClick.id,
          });
          JumpUtils.jumpToRegistration(detail, deviceId);
        }}
      />
    </View>
  );
});

CardList.propTypes = {
  productId: PropTypes.string,
  detail: PropTypes.object,
  accessories: PropTypes.object,
  deviceId: PropTypes.string,
  region: PropTypes.string,
};

const styles = StyleSheet.create({
  rects: {
    flexDirection: 'row',
    marginTop: 13,
    maxWidth: '100%',
    flexWrap: 'wrap',
  },
  rectsTop: {
    marginTop: 0,
  },
  active: {
    color: '#77BC1F',
    fontSize: 14,
    lineHeight: 16,
    marginTop: 3,
  },

  maintenance: {
    color: '#DA322B',
    fontSize: 14,
    lineHeight: 16,
    marginTop: 3,
  },

  inactive: {
    fontSize: 13,
    color: '#000000',
    lineHeight: 16,
    marginTop: 3,
  },
  moreCardStyle: {
    color: '#3C3936',
    opacity: 0.6,
  },
});

export default memo(CardList);
