/*
 * @Author: <EMAIL>
 * @Date: 2023-11-17 10:21:14
 * @LastEditors: cdlsp
 * @LastEditTime: 2024-03-26 14:22:52
 * @FilePath: /chervonreactnative/Z6/src/pages/panel/components/KeyButton.js
 * @Description: 钥匙解锁按钮组件
 *
 */
import React from 'react';
import {StyleSheet, TouchableWithoutFeedback} from 'react-native';
import {CVNIcon} from 'cvn-panel-kit';
import PropTypes from 'prop-types';

/**
 * @description: 钥匙解锁按钮组件
 */
export const KeyButton = ({
  onLongPress = () => {},
  onPress = () => {},
  isLock = true,
  hidden = false,
}) => {
  return !isLock || hidden ? null : (
    <TouchableWithoutFeedback
      style={styles.container}
      delayLongPress={2500}
      onPress={() => {
        onPress(!isLock);
      }}
      onLongPress={() => {
        onLongPress(!isLock);
      }}>
      <CVNIcon
        style={styles.circleIcon}
        source={require('@assets/z6/z6_icon_lock.png')}
      />
    </TouchableWithoutFeedback>
  );
};

KeyButton.propTypes = {
  isLock: PropTypes.bool,
  hidden: PropTypes.bool,
  onPress: PropTypes.func,
  onLongPress: PropTypes.func,
};

const styles = StyleSheet.create({
  circle: {
    width: 100,
    height: 100,
  },
  circleIcon: {
    width: 100,
    height: 100,
    position: 'absolute',
    top: '45%',
    left: '50%',
    marginTop: -50,
    marginLeft: -50,
  },
  container: {
    width: 100,
    height: 100,
  },
});

export default React.memo(KeyButton);
