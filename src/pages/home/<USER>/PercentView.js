/*
 * @Author: <EMAIL>
 * @Date: 2023-11-17 10:21:14
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2024-01-24 23:37:28
 * @FilePath: /Z6/src/pages/battery-detail/components/PercentView.js
 * @Description: 电池百分比组件
 *
 */
import React from 'react';
import {StyleSheet, View, Text} from 'react-native';
import PropTypes from 'prop-types';

export const PercentView = ({style = {}, value = 0}) => {
  const formatValue = value >= 100 ? 100 : value;
  return (
    <View style={[styles.percentView, style]}>
      <Text style={[styles.percentText]}>{formatValue}</Text>
      <Text style={[styles.symbolText]}>%</Text>
    </View>
  );
};
PercentView.propTypes = {
  style: PropTypes.object,
  value: PropTypes.number,
};

const styles = StyleSheet.create({
  percentView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  percentText: {
    fontSize: 20,
    color: '#FFFFFF',
    fontWeight: 'bold',
    lineHeight: 24,
  },
  symbolText: {
    fontSize: 11,
    color: '#FFFFFF',
    fontWeight: 'bold',
    lineHeight: 24,
  },
});
export default PercentView;
