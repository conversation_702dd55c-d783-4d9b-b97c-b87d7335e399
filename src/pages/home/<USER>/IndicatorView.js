/*
 * @Author: <EMAIL>
 * @Date: 2023-11-17 10:21:14
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2024-01-25 01:09:32
 * @FilePath: /Z6/src/pages/panel/components/IndicatorView.js
 * @Description: 指示器组件
 *
 */
import React from 'react';
import {View, StyleSheet, ViewPropTypes, Text} from 'react-native';
import {CVNIcon} from 'cvn-panel-kit';
import PropTypes from 'prop-types';

export const IndicatorView = ({
  style = {},
  source = require('@assets/z6/z6_icon_running_speed.png'),
  name = '',
  value = '',
}) => {
  return (
    <View style={[styles.container, style]}>
      <CVNIcon style={styles.lockIcon} source={source} size={20} />
      <View style={styles.textView}>
        <Text style={styles.nameText}>{name}</Text>
        <Text style={styles.valueText}>{value}</Text>
      </View>
    </View>
  );
};
IndicatorView.propTypes = {
  style: ViewPropTypes.style,
  source: PropTypes.any,
  name: PropTypes.string,
  value: PropTypes.string,
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
  },
  textView: {
    marginLeft: 5,
    marginTop: 1.5,
  },
  nameText: {
    fontSize: 14,
    color: '#666666',
  },
  valueText: {
    marginTop: 3.5,
    fontSize: 16,
    color: '#000000',
  },
});
