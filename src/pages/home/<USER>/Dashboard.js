/*
 * @Author: <EMAIL>
 * @Date: 2023-11-21 17:03:54
 * @LastEditors: cdlsp
 * @LastEditTime: 2024-04-12 15:16:57
 * @FilePath: /chervonreactnative/Z6/src/pages/panel/components/Dashboard.js
 * @Description:  面板首页仪表盘组件
 */

import LinearGradient from 'react-native-linear-gradient';
import React, {memo} from 'react';
import {Text, View, StyleSheet} from 'react-native';
import Strings from '@i18n';
import {inject} from 'mobx-react';
import {CVNIcon} from 'cvn-panel-kit';
import {IndicatorView} from '@pages/home/<USER>';
import PropTypes from 'prop-types';
import {CURRENT_STATUS} from '@utils/home';
const EMPTY = '--';

/**
 * @description: 充电图标
 */
export const chargingIcon = () => {
  return (
    <CVNIcon
      style={styles.leftIcon}
      source={require('@assets/z6/z6_icon_charging.png')}
    />
  );
};
/**
 * @description: 割草图标
 */
const mowingIcon = () => {
  return (
    <CVNIcon
      style={styles.leftIcon}
      source={require('@assets/z6/z6_icon_mowing.png')}
    />
  );
};

/**
 * @description: 剩余工作时间Text
 */
const remaindingText = () => {
  return (
    <Text style={styles.statusText}>
      {Strings.getLang('rn_4913_panelhome_remaining_textview_text')}
    </Text>
  );
};

/**
 *  @description: 速度icon组件
 *  @param runningSpeed 速度
 * @param defaultFlag ture为灰色状态 false为正常展示
 */
const runningSpeedView = (runningSpeed, defaultFlag = false) => {
  return (
    <IndicatorView
      source={
        defaultFlag
          ? require('@assets/z6/z6_icon_running_speed_gray.png')
          : require('@assets/z6/z6_icon_running_speed.png')
      }
      name={Strings.getLang('rn_4913_panelhome_runningspeed_textview_text')}
      value={runningSpeed}
    />
  );
};

/**
 *  @description: 转速icon组件
 *  @param bladeRotation 转速
 *  @param defaultFlag ture为灰色状态 false为正常展示
 */
const rotationView = (bladeRotation, defaultFlag = false) => {
  return (
    <IndicatorView
      style={styles.rotationView}
      source={
        defaultFlag
          ? require('@assets/z6/z6_icon_blade_rotation_gray.png')
          : require('@assets/z6/z6_icon_blade_rotation.png')
      }
      name={Strings.getLang('rn_4913_panelhome_bladerotation_textview_text')}
      value={bladeRotation}
    />
  );
};

/**
 * @description:剩余时间组件
 * @param remainTime 剩余时间
 * @param flag ture为工作时间 false为充电时间
 */
const remaintimeView = (remainTime, flag) => {
  return (
    <IndicatorView
      style={styles.rotationView}
      source={require('@assets/z6/z6_icon_time.png')}
      name={
        flag
          ? Strings.getLang('rn_4913_panelhome_remainingruntime_textview_text')
          : Strings.getLang(
              'rn_4913_panelhome_remainingchargingtime_textview_text',
            )
      }
      value={remainTime}
    />
  );
};

/**
 * @description: 仪表盘左侧电量view
 * @param connected 是否连接
 * @param battery 电量
 */
const batteryShowView = (connected, battery) => {
  if (connected && battery) {
    return (
      <>
        <CVNIcon
          size={18}
          style={styles.batteryIcon}
          source={require('@assets/z6/z6_icon_remaining_battery.png')}
        />
        <Text style={styles.batteryText}>{battery}</Text>
        <Text style={styles.batteryUnit}>%</Text>
      </>
    );
  }
  return <Text style={styles.batteryEmptyText}>- -</Text>;
};

/**
 * @description: 仪表盘左侧状态view
 * @param connected 是否连接
 * @param currentStatus 当前状态
 */
const leftBotttomView = (connected, currentStatus) => {
  if (!connected) {
    return null;
  }
  switch (currentStatus) {
    case CURRENT_STATUS.RUNNING:
      return remaindingText();
    case CURRENT_STATUS.CHARGING:
      return chargingIcon();
    case CURRENT_STATUS.MOWING:
      return mowingIcon();
    default:
      return null;
  }
};

/**
 * @description: 仪表盘右侧状态view
 * @param connected 是否连接
 * @param currentStatus 当前状态
 * @param runningSpeed 速度
 * @param bladeRotation 转速
 * @param remainRunningTime 剩余工作时间
 * @param remainChargingTime 剩余充电时间
 */
const rightStatusView = (
  connected,
  currentStatus,
  runningSpeed,
  bladeRotation,
  remainRunningTime,
  remainChargingTime,
) => {
  if (!connected || bladeRotation === undefined) {
    return null;
  }
  switch (currentStatus) {
    case CURRENT_STATUS.RUNNING:
      return (
        <View style={styles.topRightBox}>
          {runningSpeedView(runningSpeed)}
          {rotationView(bladeRotation)}
        </View>
      );
    case CURRENT_STATUS.CHARGING:
      return (
        <View style={styles.topRightBox}>
          {remaintimeView(remainChargingTime, false)}
          {rotationView(bladeRotation)}
        </View>
      );
    case CURRENT_STATUS.MOWING:
      return (
        <View style={styles.topRightBox}>
          {runningSpeedView(runningSpeed)}
          {rotationView(bladeRotation)}
          {remaintimeView(remainRunningTime, true)}
        </View>
      );
    default:
      return null;
  }
};

/**
 * @description:仪表盘组件 电量、电池信息、速度、转速、剩余时间等信息
 */
export const Dashboard = inject(store => ({
  home: store.panel.home,
}))(({connected, home = {}}) => {
  const {
    currentStatus = '',
    runningSpeed,
    bladeRotation,
    remainRunningTime,
    remainChargingTime,
    totalBatteryPercentValue,
  } = home;
  return (
    <View style={styles.statistic}>
      <View style={[styles.charge, styles.chargeBack]}>
        <LinearGradient
          style={[styles.charge, styles.bg]}
          start={{x: 0, y: 0}}
          end={{x: 0, y: 1}}
          colors={
            connected ? ['#77BC1FFF', '#77BC1F00'] : ['#C1C1C1FF', '#C1C1C100']
          }
        />
        <View style={styles.batteryTextBox}>
          {batteryShowView(connected, totalBatteryPercentValue)}
        </View>
        {leftBotttomView(connected, currentStatus)}
      </View>
      {rightStatusView(
        connected,
        currentStatus,
        runningSpeed,
        bladeRotation,
        remainRunningTime,
        remainChargingTime,
      )}
      {/* 未连接时，占位 */}

      {!connected || bladeRotation === undefined ? (
        <View style={styles.topRightBox}>
          {runningSpeedView(EMPTY, true)}
          {rotationView(EMPTY, true)}
        </View>
      ) : null}
    </View>
  );
});

Dashboard.propTypes = {
  connected: PropTypes.bool,
  home: PropTypes.object,
};

const styles = StyleSheet.create({
  statistic: {
    flexDirection: 'row',
  },
  bg: {
    position: 'absolute',
    top: 0,
    left: 0,
    opacity: 0.33,
  },
  charge: {
    width: 145,
    height: 196,
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    alignItems: 'center',
    paddingVertical: 25,
    position: 'relative',
  },
  chargeBack: {
    marginLeft: 16,
  },
  batteryView: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'yellow',
  },
  batteryIcon: {},
  leftIcon: {
    marginTop: 19,
    width: 58,
    height: 58,
  },
  left: {
    lineHeight: 48,
    color: '#000000',
    fontSize: 40,
    fontWeight: '500',
  },
  batteryTextBox: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  batteryText: {
    marginLeft: 4,
    color: '#000000',
    fontSize: 30,
    fontWeight: '500',
  },
  batteryUnit: {
    marginLeft: 1.5,
    marginTop: 8,
    fontSize: 15,
    color: '#000000',
    fontWeight: '500',
  },
  batteryEmptyText: {
    marginTop: -8,
    fontSize: 50,
    color: '#000000',
    fontWeight: '500',
  },
  statusText: {
    lineHeight: 18,
    fontSize: 14,
    color: '#666666',
    marginTop: 6.5,
  },

  topRightBox: {
    marginLeft: 35,
    marginTop: 16,
    flex: 1,
  },
  rotationView: {
    marginTop: 12,
  },
});

export default memo(Dashboard);
