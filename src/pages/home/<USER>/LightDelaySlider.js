/*
 * @Author: <EMAIL>
 * @Date: 2023-12-12 16:30:21
 * @LastEditors: cdlsp
 * @LastEditTime: 2024-04-24 15:17:38
 * @FilePath: /chervonreactnative/Z6/src/pages/panel/components/LightDelaySlider.js
 * @Description: 面板延迟关灯时间滑动条组件
 */

import {StyleSheet, Text, View} from 'react-native';
import {inject} from 'mobx-react/native';
import React, {useRef, useState, useEffect} from 'react';
import {Slider} from 'react-native-elements';
import {CVNIcon} from 'cvn-panel-kit';
import {DEVICE_WIDTH} from 'IOTRN/src/utils/device';
import PropTypes from 'prop-types';
const lightOffStep = 60; // 延时关灯每档间隔
const minimumValue = 0; // 最小值
const maximumValue = 300; // 最大值
const gearsArray = [1, 2, 3, 4, 5, 6]; // 档位数组
const minTrackTintColor = '#77BC1F';
const maxTrackTintColor = '#D8D8D8';

/**
 *
 * @description thumb-children滑块组件
 * @param {*} number
 */
const thumbChildren = value => {
  return (
    <View style={styles.outer}>
      <CVNIcon size={33} source={require('@assets/z6/z6_icon_track_dot.png')} />
      {value > minimumValue && value < maximumValue ? (
        <View style={styles.val}>
          <Text style={styles.tipText}>{value}</Text>
        </View>
      ) : null}
    </View>
  );
};

/**
 * @description: 定位白点
 */
const WhiteDot = props => {
  return props.index === props.selectIndex ? null : (
    <View
      key={props?.index}
      style={[styles.dotView, styles.dotView1, {left: props?.cusLeft}]}
    />
  );
};

WhiteDot.propTypes = {
  index: PropTypes.number,
  selectIndex: PropTypes.number,
  cusLeft: PropTypes.number,
};

/***
 * @description: 根据滑动条值转换显示值
 */
export const getBubbleCompuValue = value => {
  switch (value) {
    case 0:
      return 0;
    case 60:
      return 5;
    case 120:
      return 15;
    case 180:
      return 30;
    case 240:
      return 100;
    case 300:
      return 300;
    default:
      return 0;
  }
};

/**
 * @description: 面板延迟关灯时间滑动条组件
 */
export const LightDelaySlider = inject(store => ({
  lightDelayOffTimeLevel: store.panel.lightDelayOffTimeLevel,
}))(
  ({
    lightDelayOffTimeLevel,
    style = {},
    onSlidingComplete,
    delayOffTimeTextChange,
  }) => {
    const [bubbleValue, setBubbleValue] = useState(
      lightDelayOffTimeLevel * lightOffStep,
    ); // 气泡显示值
    const [sliderValue, setSliderValue] = useState(
      lightDelayOffTimeLevel * lightOffStep,
    ); // 滑动条值
    const currentIndex = useRef(lightDelayOffTimeLevel); // 用于储存当前档位，不引起组件更新
    const setMobxDataFlag = useRef(true); // 标志位,保证lightDelayOffTimeLevel变化时，只调用一次delayOffTimeTextChange
    useEffect(() => {
      setBubbleValue(
        getBubbleCompuValue(lightDelayOffTimeLevel * lightOffStep),
      );
      if (setMobxDataFlag.current) {
        setMobxDataFlag.current = false;
        delayOffTimeTextChange(
          getBubbleCompuValue(lightDelayOffTimeLevel * lightOffStep),
        );
      }
      currentIndex.current = lightDelayOffTimeLevel;
    }, [delayOffTimeTextChange, lightDelayOffTimeLevel]);

    /**
     * @description: 滑动条值改变
     */
    const valueChangeAction = indexValue => {
      currentIndex.current = indexValue / lightOffStep;
      setSliderValue(indexValue);
      setBubbleValue(getBubbleCompuValue(indexValue));
      delayOffTimeTextChange(getBubbleCompuValue(indexValue));
    };

    return (
      <View style={[styles.wrapper, style]}>
        <Text style={styles.min}>{minimumValue}</Text>
        <Text style={styles.max}>{maximumValue}</Text>
        {gearsArray.map((item, index) => {
          const marginX = (DEVICE_WIDTH - 83 - 6 * 4) / 5;
          const cusLeft = 9 + index * (4 + marginX);
          return (
            <WhiteDot
              key={item}
              index={index}
              cusLeft={cusLeft}
              selectIndex={currentIndex.current}
            />
          );
        })}
        <Slider
          onSlidingComplete={indexValue => {
            onSlidingComplete(indexValue / lightOffStep);
          }}
          style={styles.container}
          value={sliderValue}
          onValueChange={value => {
            valueChangeAction(value);
          }}
          minimumValue={minimumValue}
          maximumValue={maximumValue}
          thumbTouchSize={styles.thumbTouchSize}
          minimumTrackTintColor={minTrackTintColor}
          maximumTrackTintColor={maxTrackTintColor}
          trackStyle={styles.track}
          thumbStyle={styles.thumbStyle}
          step={lightOffStep}
          thumbProps={{
            children: thumbChildren(bubbleValue),
          }}
        />
      </View>
    );
  },
);

LightDelaySlider.propTypes = {
  style: PropTypes.object,
  lightDelayOffTimeLevel: PropTypes.number,
  onSlidingComplete: PropTypes.func,
  delayOffTimeTextChange: PropTypes.func,
};

/**
 * @description: 面板延迟关灯时间替身滑动条组件
 */
export const LightDelayFreezeSlider = ({style}) => {
  return (
    <View style={[styles.wrapper, style]}>
      <Text style={styles.min}>{minimumValue}</Text>
      <Text style={styles.max}>{maximumValue}</Text>
      {gearsArray.map((item, index) => {
        const marginX = (DEVICE_WIDTH - 83 - 6 * 4) / 5;
        const cusLeft = 9 + index * (4 + marginX);
        return (
          <WhiteDot
            key={item}
            index={index}
            cusLeft={cusLeft}
            selectIndex={0}
          />
        );
      })}
      <Slider
        style={styles.container}
        value={0}
        minimumValue={minimumValue}
        maximumValue={maximumValue}
        thumbTouchSize={styles.thumbTouchSize}
        minimumTrackTintColor={minTrackTintColor}
        maximumTrackTintColor={maxTrackTintColor}
        trackStyle={styles.track}
        thumbStyle={styles.thumbStyle}
        step={lightOffStep}
        thumbProps={{
          children: thumbChildren(0),
        }}
      />
    </View>
  );
};

LightDelayFreezeSlider.propTypes = {
  style: PropTypes.object,
};

const styles = StyleSheet.create({
  wrapper: {
    position: 'relative',
    flexDirection: 'row',
    height: 60,
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
  },
  min: {
    position: 'absolute',
    left: 0,
    top: 17.5,
    fontSize: 12,
    color: '#000',
  },
  max: {
    position: 'absolute',
    right: 0,
    top: 17.5,
    fontSize: 12,
    color: '#000',
  },
  dotView: {
    width: 4,
    height: 4,
    borderRadius: 2,
    position: 'absolute',
    bottom: 14,
    backgroundColor: 'red',
  },
  dotView1: {
    zIndex: 10,
    backgroundColor: '#FFF',
  },
  track: {
    height: 14,
    borderRadius: 7,
    backgroundColor: '#0000ff',
  },
  thumb: {
    height: 20,
    width: 20,
    backgroundColor: '#77BC1F',
    borderColor: '#fff',
    borderWidth: 2,
    shadowRadius: 10,
    shadowOpacity: 0.1,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
  },
  extra: {
    marginLeft: 20,
    width: 50,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  text: {
    color: '#77BC1F',
    fontSize: 17,
    marginRight: 5,
  },
  outer: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    bottom: 2,
  },
  inner: {
    width: 15,
    height: 15,
    backgroundColor: 'blue',
    borderRadius: 7.5,
  },
  val: {
    position: 'absolute',
    backgroundColor: '#77BC1F',
    height: 25,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12.5,
    top: -32,
    width: 60,
    left: -20,
  },
  container: {
    flex: 1,
    height: 33,
  },
  thumbStyle: {
    height: 20,
    width: 20,
    backgroundColor: 'transparent',
  },
  tipText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  thumbTouchSize: {
    width: 20,
    height: 20,
  },
});
