/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-02-09 11:35:30
 * @LastEditors: cdlsp
 * @LastEditTime: 2024-07-26 11:31:26
 * @FilePath: /chervonreactnative/Z6/src/pages/panel/components/ErrorView.js
 * @Description: 升级、设备告警view封装
 */
import React from 'react';
import {
  StyleSheet,
  View,
  TouchableOpacity,
  Text,
  ViewPropTypes,
} from 'react-native';
import {CVNIcon} from 'cvn-panel-kit';
import PropTypes from 'prop-types';

export const ErrorView = ({
  text = '',
  isShow = true,
  style = {},
  onPress = () => {},
  source,
}) => {
  if (!isShow) {
    return null;
  }
  return (
    <View style={[styles.errorView, style]}>
      <TouchableOpacity
        onPress={onPress}
        hitSlop={{top: 10, right: 10, left: 10, bottom: 10}}>
        <View style={styles.errorInView}>
          <View style={styles.leftBox}>
            <CVNIcon style={styles.iconView} source={source} size={22} />
            <Text numberOfLines={1} style={styles.errorText}>
              {text}
            </Text>
          </View>
          <CVNIcon
            style={styles.arrowIcon}
            source={require('@assets/z6/z6_btn_arrow_right.png')}
            size={20}
          />
        </View>
      </TouchableOpacity>
    </View>
  );
};
ErrorView.propTypes = {
  text: PropTypes.string,
  isShow: PropTypes.bool,
  style: ViewPropTypes.style,
  onPress: PropTypes.func,
  source: PropTypes.node,
};
const styles = StyleSheet.create({
  errorView: {
    height: 48,
    paddingHorizontal: 14,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    // opacity: 0.65,
    justifyContent: 'center',
    marginBottom: 13,
  },
  errorInView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  leftBox: {
    flexDirection: 'row',
    alignItems: 'center',
    flexShrink: 2,
  },
  iconView: {},
  arrowIcon: {
    // flexShrink: 2,
  },
  errorText: {
    marginLeft: 12,
    fontSize: 15,
    color: '#000000',
    flexShrink: 2,
    fontWeight: '500',
  },
});
