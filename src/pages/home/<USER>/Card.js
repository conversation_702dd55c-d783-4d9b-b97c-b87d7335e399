/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-02-09 11:35:35
 * @LastEditors: cdlsp
 * @LastEditTime: 2024-04-11 12:14:32
 * @FilePath: /chervonreactnative/Z6/src/pages/panel/components/Card.js
 * @Description: 首页card封装组件
 *
 */
import {StyleSheet, Text, View} from 'react-native';
import React, {memo} from 'react';
import {CVNIcon} from 'cvn-panel-kit';
import PropTypes from 'prop-types';
/**
 * @description: 首页card封装组件
 */
export const Card = ({
  children,
  title,
  icon,
  subTitle,
  titleStyle = {},
  cStyle = {},
  disabled,
}) => {
  return (
    <View style={[styles.container, cStyle]}>
      <View style={styles.header}>
        <CVNIcon size={34} style={styles.icon} source={icon} />
        <Text style={[styles.title, titleStyle]}>{title}</Text>
        {subTitle ? (
          <View style={styles.subTitleBox}>
            <Text style={styles.sub} />
            <View style={styles.shortLine} />
            <Text style={styles.subTitle}>{subTitle}</Text>
          </View>
        ) : null}
      </View>
      {children}

      {disabled ? <View style={styles.disabled} /> : null}
    </View>
  );
};

Card.propTypes = {
  children: PropTypes.node,
  title: PropTypes.string,
  icon: PropTypes.any,
  subTitle: PropTypes.string,
  titleStyle: PropTypes.object,
  cStyle: PropTypes.object,
  disabled: PropTypes.bool,
};

const styles = StyleSheet.create({
  container: {
    paddingTop: 13.5,
    paddingBottom: 15,
    paddingHorizontal: 15,
    backgroundColor: '#fff',
    borderRadius: 5,
    marginHorizontal: 15,
    marginBottom: 15,
    position: 'relative',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    // backgroundColor: 'blue',
  },
  icon: {},
  title: {
    marginLeft: 10.5,
    color: '#000000',
    fontSize: 17,
    fontWeight: '400',
  },
  subTitleBox: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  sp: {
    marginHorizontal: 30,
    color: '#000000',
    opacity: 0.5,
    fontSize: 15,
  },
  shortLine: {
    marginHorizontal: 12,
    backgroundColor: '#000000',
    opacity: 0.5,
    width: 0.5,
    height: 15,
  },
  sub: {
    color: '#000000',
    opacity: 0.7,
    fontSize: 12,
  },
  subTitle: {
    color: '#000000',
    fontSize: 12,
    lineHeight: 14.5,
    flexShrink: 2,
  },
  disabled: {
    position: 'absolute',
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
    opacity: 0.4,
    backgroundColor: '#ffffff',
  },
});

export default memo(Card);
