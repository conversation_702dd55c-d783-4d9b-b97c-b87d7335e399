/*
 * @Author: <EMAIL>
 * @Date: 2023-11-21 17:56:35
 * @LastEditors: cdlsp
 * @LastEditTime: 2024-05-07 16:56:40
 * @FilePath: /chervonreactnative/Z6/src/pages/panel/components/BatteryIconView.js
 * @Description: 电池图标组件
 *
 */
import React from 'react';
import {StyleSheet, View, Text} from 'react-native';
import Strings from '@i18n';
import {CVNIcon} from 'cvn-panel-kit';
import {PercentView} from './PercentView';
import PropTypes from 'prop-types';
const MCVNIcon = React.memo(CVNIcon);

/**
 * @description: 电池图标组件
 */
export const BatteryIconView = ({item = {}, index = 0, style = {}}) => {
  const {
    didHave = false,
    isCharging = false,
    isError = false,
    value = 0,
  } = item;
  const source = didHave
    ? require('@assets/z6/z6_icon_battery_pack.png')
    : undefined;
  const chargingStyle = isCharging
    ? {justifyContent: 'flex-start'}
    : {justifyContent: 'center'};
  if (!didHave) {
    return <View style={style} />;
  }
  return (
    <MCVNIcon
      style={[styles.batteryDefault, chargingStyle, style]}
      resizeMode="stretch"
      source={source}>
      {isCharging ? (
        <View style={styles.percentBox}>
          <PercentView style={styles.margintop} value={value} />
          <Text style={styles.chargingText}>
            {Strings.getLang('rn_4913_batterydetail_charging_textview_text')}
          </Text>
        </View>
      ) : (
        <PercentView value={value} />
      )}
      {isError && (
        <MCVNIcon
          size={20}
          style={styles.errorIcon}
          source={require('@assets/z6/z6_icon_battery_error.png')}
        />
      )}
    </MCVNIcon>
  );
};
BatteryIconView.propTypes = {
  item: PropTypes.object,
  index: PropTypes.number,
  style: PropTypes.object,
};
const styles = StyleSheet.create({
  batteryDefault: {
    alignItems: 'center',
  },
  percentBox: {
    alignItems: 'center',
  },
  chargingText: {
    fontSize: 12,
    color: '#FFFFFF',
    lineHeight: 14.5,
  },
  errorIcon: {
    position: 'absolute',
    top: 6,
    right: 1,
    zIndex: 20,
  },
  healthTitle: {
    position: 'absolute',
    bottom: 16.5,
    zIndex: 20,
    fontSize: 12,
    color: '#ffffff',
    opacity: 0.4,
  },
  margintop: {
    marginTop: 29,
  },
});

export default BatteryIconView;
