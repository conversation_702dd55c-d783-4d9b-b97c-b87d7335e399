/*
 * @Author: cdlsp
 * @Date: 2023-11-17 10:21:14
 * @LastEditors: cdlsp
 * @LastEditTime: 2024-05-21 15:30:50
 * @FilePath: /chervonreactnative/Z6/src/pages/panel/components/RectView.js
 * @Description: 一屏两卡片组件
 *
 */
import {StyleSheet, Text, TouchableWithoutFeedback, View} from 'react-native';
import React, {isValidElement} from 'react';
import {CVNIcon} from 'cvn-panel-kit';
import PropTypes from 'prop-types';

export const RectView = ({
  icon,
  title,
  titleExtraStyle = {},
  extra,
  isLast,
  showTip = false,
  onPress = () => {},
  disabled,
}) => {
  return (
    <View style={[styles.rect, isLast ? {} : styles.containerRight]}>
      <TouchableWithoutFeedback
        onPress={() => {
          if (!disabled) {
            onPress();
          }
        }}>
        <View>
          {showTip && <View style={styles.tip} />}
          <CVNIcon size={34} source={icon} />
          <Text style={[styles.rectLabel, titleExtraStyle]}>{title}</Text>
          {isValidElement(extra) ? (
            extra
          ) : (
            <Text style={[styles.rectDesc]}>{extra}</Text>
          )}
        </View>
      </TouchableWithoutFeedback>

      {disabled ? <View style={[styles.disabled]} /> : null}
    </View>
  );
};

RectView.propTypes = {
  icon: PropTypes.any,
  title: PropTypes.string,
  titleExtraStyle: PropTypes.object,
  extra: PropTypes.any,
  isLast: PropTypes.bool,
  showTip: PropTypes.bool,
  onPress: PropTypes.func,
  disabled: PropTypes.bool,
};

const styles = StyleSheet.create({
  rect: {
    borderRadius: 5,
    flex: 1,
    backgroundColor: '#fff',
    paddingTop: 10,
    paddingBottom: 12,
    paddingHorizontal: 10,
    position: 'relative',
  },
  tip: {
    width: 7,
    height: 7,
    borderRadius: 3.5,
    backgroundColor: '#FF4646',
    position: 'absolute',
    top: -3,
    right: 3,
  },
  rectLabel: {
    color: '#000',
    fontSize: 16,
    lineHeight: 19,
    marginTop: 6,
  },
  rectDesc: {
    color: '#77BC1F',
    fontSize: 14,
    lineHeight: 16,
    marginTop: 3,
  },
  disabled: {
    position: 'absolute',
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
    opacity: 0.4,
    backgroundColor: '#ffffff',
    borderRadius: 5,
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.05,
    shadowColor: '#000000',
    shadowRadius: 7.5,
  },
  containerRight: {
    marginRight: 15,
  },
});

export default RectView;
