/*
 * @Author: cdlsp
 * @Date: 2023-11-17 10:21:14
 * @LastEditors: cdlsp
 * @LastEditTime: 2024-03-25 19:43:38
 * @FilePath: /chervonreactnative/Z6/src/pages/panel/components/SegmentView.js
 * @Description: 面板-多选项switch组件
 *
 */
import React, {useState, useEffect, memo} from 'react';
import {View, StyleSheet, Text, TouchableWithoutFeedback} from 'react-native';
import PropTypes from 'prop-types';

export const SegmentView = ({
  selectIndex = 0,
  style = {},
  onItemSelect = () => {},
  data = [],
}) => {
  const [selIndex, setSelIndex] = useState(0);
  useEffect(() => {
    setSelIndex(selectIndex);
  }, [selectIndex]);
  return (
    <View style={styles.container}>
      {data.map((item, index) => {
        const selected = selIndex === index;
        return (
          <TouchableWithoutFeedback
            key={item.title}
            onPress={() => {
              setSelIndex(index);
              onItemSelect(item, index);
            }}>
            <View
              style={[
                styles.buttonView,
                selected ? styles.activeView : styles.inActiveView,
              ]}>
              <Text
                style={[
                  styles.titleText,
                  selected ? styles.activeText : styles.inActiveText,
                ]}>
                {item.title}
              </Text>
            </View>
          </TouchableWithoutFeedback>
        );
      })}
    </View>
  );
};

SegmentView.propTypes = {
  style: PropTypes.object,
  selectIndex: PropTypes.number,
  data: PropTypes.array,
  onItemSelect: PropTypes.func,
};
const styles = StyleSheet.create({
  container: {
    padding: 4,
    marginTop: 24,
    backgroundColor: '#EDEEEF',
    borderRadius: 25,
    flexDirection: 'row',
    alignItems: 'center',
    height: 50,
  },
  buttonView: {
    borderRadius: 21,
    height: 42,
    width: '33%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  activeView: {
    backgroundColor: '#77BC1F',
  },
  inActiveView: {
    backgroundColor: 'transparent',
  },
  titleText: {
    fontSize: 14,
  },
  activeText: {
    color: '#FFF',
    fontWeight: '500',
  },
  inActiveText: {
    color: '#00000099',
    fontWeight: '400',
  },
});

export default memo(SegmentView);
