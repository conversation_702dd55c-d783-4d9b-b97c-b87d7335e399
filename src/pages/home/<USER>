/*
 * @Author: <EMAIL>
 * @Date: 2023-11-23 17:55:15
 * @LastEditors: cdlsp
 * @LastEditTime: 2024-07-11 16:04:44
 * @FilePath: /chervonreactnative/Z6/src/pages/panel/Home.js
 * @Description: 面板首页
 *
 */
import React, {Component} from 'react';
import {
  mobile,
  CommonEmitter,
  Utils,
  WhiteSpace,
  PageView,
  CVNIcon,
  device,
} from 'cvn-panel-kit';
import {z6Device} from '@feature/Z6NativeApi';
import {HeaderView} from '@components';
import topicMap from 'src/utils/topic.js';
import {DEVICE_WIDTH, isIphoneX} from '@utils/device/index.js';
import {
  Text,
  View,
  StyleSheet,
  Image,
  ScrollView,
  Platform,
  Vibration,
  TouchableOpacity,
  AppState,
} from 'react-native';
import {inject, observer} from 'mobx-react/native';
import Strings from '@i18n';
import LinearGradient from 'react-native-linear-gradient';
import {
  Card,
  CardList,
  FaultView,
  SegmentView,
  LightDelaySlider,
  BatteryView,
  KeyButton,
  Dashboard,
  LightDelayFreezeSlider,
} from '@pages/home/<USER>';
import DeviceInfo from 'react-native-device-info';
import {TouchableWithoutFeedback} from 'react-native-gesture-handler';
import {
  appSendSetCmd,
  parseIotData,
  queryRealTimeCmd,
  queryRealTimeCmdString,
} from 'IOTRN/src/utils/cmd';
import {
  BACK_UP_ALERT_ARRAY,
  DAYTIME_LIGHT_ARRAY,
  CMD_DICTIONARY,
  CURRENT_STATUS,
  PANEL_ACTION,
} from 'IOTRN/src/utils/home';
import {LogUtils} from '@cvn/rn-panel-kit/src/utils';
import tracker, {PANEL_HOME_PAGE_ELEMENTS_MAP} from '@tracking/index';
const {PermissionUtils, MessageUtils} = Utils;
import _ from 'lodash-es';
const MCVNIcon = React.memo(CVNIcon);
// 实时数据轮询获取心跳时间
const heartbeatTime = Platform.OS === 'ios' ? 2000 : 3000;
// 延时调用隐藏loading时间
const delayHideLoadTime = Platform.OS === 'ios' ? 2000 : 2500;
// 蓝牙重连次数
const BLE_RETRY_Times = 3;
// 连接状态
const connectStatusMap = {
  NOTCONNECTED: 0, // 未连接
  CONNECTING: 1, // 连接中
  CONNECTED: 2, // 已连接
  FAILED: 3, // 连接失败
};

// 蓝牙状态
const bleChangeTypeMap = {
  0: 'notOpened',
  1: 'opened',
  2: 'notAuthorized',
};

// 设备消息类型
const deviceMessageType = 2;
@inject('panelActions', 'panel')
@observer
export default class Home extends Component {
  constructor(props) {
    super(props);
    this.actionWithType = _.throttle(this.actionWithType.bind(this), 800);
    this.connectMap = {
      0: Strings.getLang('rn_4913_panelhome_disconnected_textview_text'),
      1: Strings.getLang('rn_4913_panelhome_connecting_textview_text'),
      2: Strings.getLang('rn_4913_panelhome_connected_textview_text'),
      3: Strings.getLang('rn_4913_panelhome_disconnected_textview_text'),
    };
    this.state = {
      lightDelayOffTimeText: 0,
      showBatteryInfo: false,
      connectDesc: this.connectMap[1],
    };
    this.retryTimes = 0;
    this.lightDelayOffTimeSetFlag = true;
    LogUtils.init();
    this.appStateSubscription = null;
    this.intervalID = null;
    this.hideLoadingTimer = null;
  }

  componentDidMount() {
    tracker.loadPage();
    this.dealWithInitialBluetoothChange();
    this.requestZ6BleConnect();
    this.dealWithRNContainerPushOrPop();
    this.dealWithDetaiList();
    this.dealWithAndroidBack();
    this.dealWithAppDidComeForeground();
    this.dealWithPushNotification();
    this.dealWithMsg();
    this.props.panelActions.subscribeWithTopic();
    this.dealWithTopic();
  }

  componentWillUnmount() {
    tracker.leavePage({
      willInRn: false,
    });
    this.dealWithWillUnmount();
    this.removeRealDataTimer();
    this.removeAllDelayTimer();
    mobile.hideLoading();
  }

  /**
   * @description: 处理app从后台切到前台的事件
   */
  dealWithAppDidComeForeground = () => {
    // 监听状态改变事件
    this.appStateSubscription = AppState.addEventListener(
      'change',
      this.handleAppStateChange,
    );
  };

  /**
   * @description: 处理app状态为active
   */
  handleAppStateChange = appState => {
    if (appState === 'active') {
      this.retryTimes = 0;
      if (Platform.OS === 'android') {
        return;
      }
      this.setState({connectDesc: this.connectMap[1]});
      this.props.panelActions.setBleConnected(false);
      const {deviceId, mac} = this.props.panel;
      z6Device.requestZ6BleConnect(deviceId, mac);
    }
  };

  /**
   * @description: 消息推送监听处理
   */
  dealWithPushNotification = () => {
    CommonEmitter.addListener('didRecievePushNotification', res => {
      if (LogUtils.currentPage !== 'ViewPanelHome') {
        if (mobile.handlePushNotification) {
          mobile.handlePushNotification();
        }
      }
    });
  };

  /**
   * @description: 设备取消订阅处理
   */
  deviceUnSubscribe = () => {
    const {deviceId} = this.props.panel;
    const shadowUpdateAcceptedStr = `${topicMap.preFix}${deviceId}${topicMap.shadowUpdateAcceptedSuffix}`;
    // 订阅物模型 接收
    device.unsubscribe(shadowUpdateAcceptedStr);
  };
  /**
   * 处理topic数据
   **/
  dealWithTopic = () => {
    CommonEmitter.addListener('topicDataDidChange', res => {
      if (res === '') {
        return;
      }
      const obj = JSON.parse(res);
      // 过滤不是当前设备上报的数据,R适用，C有子设备不适用，RN做了兼容
      if (obj.type.indexOf(this.props.panel.deviceId) === -1) {
        return;
      }
      const {payload} = obj || {};
      const payloadData = JSON.parse(payload) || {};
      const {reported = {}} = payloadData?.state || {};
      this.props.panelActions.clearErrorData(reported);
    });
  };

  /**
   * @description: 处理设备消息
   */
  dealWithMsg = () => {
    MessageUtils.dealWithMessage(this.props.panel, (msgType, resObj) => {
      // 只处理设备消息
      if (msgType !== deviceMessageType) {
        return;
      }
      this.props.panelActions.setMessageData(resObj);
    });
  };

  /**
   * @description: Z6连接设备
   */
  requestZ6BleConnect = () => {
    const {deviceId, mac} = this.props.panel;
    z6Device.requestZ6BleConnect(deviceId, mac);
    CommonEmitter.addListener('deviceBleConnectStateChange', res => {
      this.dealWithBleConnectStateChange(res);
    });

    CommonEmitter.addListener('z6LocalDeviceDataChange', res => {
      this.dealWithLocalDeviceDataChange(res);
    });
  };

  /**
   *  处理监听蓝牙初始状态变化逻辑
   *  0:未开启  1:已开启  2:未授权
   *  未开启：提示用户开启蓝牙
   *  已开启：请求连接设备
   *  未授权：提示用户未授权并请求蓝牙连接
   */
  dealWithInitialBluetoothChange = () => {
    const {deviceId, mac} = this.props.panel;
    CommonEmitter.addListener('bluetoothChange', res => {
      const type = res - 0;
      const phoneBleStatus = bleChangeTypeMap[type];
      switch (phoneBleStatus) {
        case 'notOpened':
          {
            const title = Strings.getLang(
              'rn_common_ble_notopened_textview_text',
            );
            mobile.toast(title, () => {});
            this.props.panelActions.setBleConnected(false);
            this.setState({connectDesc: this.connectMap[0]});
            this.removeRealDataTimer();
          }
          break;
        case 'opened':
          {
            this.retryTimes = 0; // 重新开启蓝牙时置位
            const title = Strings.getLang('rn_common_ble_opened_textview_text');
            mobile.toast(title, () => {});
            this.removeRealDataTimer();
            this.setState({connectDesc: this.connectMap[1]});
            z6Device.requestZ6BleConnect(deviceId, mac);
          }
          break;
        case 'notAuthorized':
          {
            const title = Strings.getLang(
              'rn_common_ble_notauthorizedtitle_textview_text',
            );
            mobile.toast(title, () => {});
            this.setState({connectDesc: this.connectMap[0]});
            this.removeRealDataTimer();
          }
          break;
        default:
          break;
      }
    });
  };

  /***
   * 处理蓝牙连接状态变化
   *
   */
  dealWithBleConnectStateChange = res => {
    const tmpConnectStatus = Number(res);
    if (tmpConnectStatus === connectStatusMap.CONNECTED) {
      // 蓝牙连接成功
      this.props.panelActions.setBleConnected(true);
      this.setState({connectDesc: this.connectMap[2]});
      this.intervalFetchRealData();
      this.retryTimes = 0;
    } else if (tmpConnectStatus === connectStatusMap.CONNECTING) {
      this.props.panelActions.setBleConnected(false);
      this.setState({connectDesc: this.connectMap[1]});
      this.removeRealDataTimer();
      this.dealWithReConnect();
    } else if (tmpConnectStatus === connectStatusMap.NOTCONNECTED) {
      this.setState({connectDesc: this.connectMap[1]});
      this.props.panelActions.setBleConnected(false);
      this.removeRealDataTimer();
      this.dealWithReConnect();
    } else {
      this.setState({connectDesc: this.connectMap[0]});
      this.props.panelActions.setBleConnected(false);
      this.removeRealDataTimer();
    }
  };

  /**
   * 重连
   */
  dealWithReConnect = () => {
    const {deviceId, mac} = this.props.panel;
    if (this.retryTimes > BLE_RETRY_Times) {
      this.setState({connectDesc: this.connectMap[0]});
      this.props.panelActions.setBleConnected(false);
      return;
    }
    z6Device.requestZ6BleConnect(deviceId, mac);
    this.retryTimes++;
  };

  /**
   * @description: 定时获取实时信息的处理
   */
  intervalFetchRealData = () => {
    if (Platform.OS === 'android') {
      // android
      z6Device.stopPolling();
      z6Device.startPolling(
        queryRealTimeCmdString(),
        this.props.panel.mac || '',
        heartbeatTime,
      );
    } else {
      queryRealTimeCmd();
      if (this.intervalID) {
        clearInterval(this.intervalID);
        this.intervalID = null;
      }
      this.intervalID = setInterval(() => {
        queryRealTimeCmd();
      }, heartbeatTime);
    }
  };

  /**
   * @description: 移除实时数据获取的轮询timer
   */
  removeRealDataTimer = () => {
    if (Platform.OS === 'android') {
      // android
      z6Device.stopPolling();
    } else {
      this.intervalID && clearInterval(this.intervalID);
      this.intervalID = null;
    }
  };

  /**
   * @description:移除延时timer
   */
  removeAllDelayTimer = () => {
    this.hideLoadingTimer && clearTimeout(this.hideLoadingTimer);
  };

  /**
   * @description: 处理设备返回数据
   *
   */
  dealWithLocalDeviceDataChange = res => {
    if (typeof res === 'string' && res.length > 7) {
      parseIotData(res);
    }
  };

  /**
   * @description: 监听 原生->RN， RN->原生
   */
  dealWithRNContainerPushOrPop = () => {
    CommonEmitter.addListener('RNContainerViewWillAppear', res => {
      this.props.panelActions.setIsInRNContainerVC(true);
      this.getDetailList();
      this.getDetailAccessories();
    });
    CommonEmitter.addListener('RNContainerViewWillDisAppear', res => {
      this.props.panelActions.setIsInRNContainerVC(false);
    });
  };

  /**
   * @description: 安卓物理返回键处理
   */
  dealWithAndroidBack = () => {
    if (Platform.OS === 'android') {
      const {navigation} = this.props;
      CommonEmitter.addListener('keyBackDown', res => {
        // 物理按键的时候处理
        if (navigation.canGoBack()) {
          navigation.goBack();
        } else {
          this.dealWithWillUnmount();
          mobile.back();
        }
      });
    }
  };

  /**
   * @description: 处理设备详情
   */
  dealWithDetaiList = () => {
    CommonEmitter.addListener('NAVIGATOR_ON_WILL_FOCUS', () => {
      this.getDetailList();
    });
    this.getDetailList();
    this.getDetailAccessories();
  };

  /**
   * @description: 获取设备详情请求
   */
  getDetailList = () => {
    this.props.panelActions.deviceDetail({
      req: this.props.panel.deviceId,
    });
  };

  /**
   * @description: 获取设备外设信息详情
   */
  getDetailAccessories = () => {
    this.props.panelActions.deviceAccessories({
      req: this.props.panel.deviceId,
    });
  };

  /**
   * @description: 取消各种订阅
   *
   */
  dealWithWillUnmount = type => {
    PermissionUtils.removeAppStateChangeListener();
    CommonEmitter.removeAllListeners('messageNotify');
    CommonEmitter.removeAllListeners('NAVIGATOR_ON_WILL_FOCUS');
    CommonEmitter.removeAllListeners('RNContainerViewWillAppear');
    CommonEmitter.removeAllListeners('RNContainerViewWillDisAppear');
    CommonEmitter.removeAllListeners('didRecievePushNotification');
    CommonEmitter.removeAllListeners('deviceBleConnectStateChange');
    CommonEmitter.removeAllListeners('z6LocalDeviceDataChange');
    CommonEmitter.removeAllListeners('bluetoothChange');
    CommonEmitter.removeAllListeners('topicDataDidChange');
    // android物理返回键监听
    if (Platform.OS === 'android') {
      CommonEmitter.removeAllListeners('keyBackDown');
    }
    this.appStateSubscription?.remove();
    if (device.unsubscribe) {
      this.deviceUnSubscribe();
    }
  };

  /**
   * @description: 出栈逻辑，区分是RN路由出栈还是原生vc出栈
   */
  dealWithGoBack = () => {
    tracker.click({
      elementId: PANEL_HOME_PAGE_ELEMENTS_MAP.returnButtonClick.id,
    });
    this.dealWithWillUnmount();
    mobile.back();
  };

  /**
   * @description: 面板操作事件
   */
  actionWithType = type => {
    this.removeRealDataTimer();
    mobile.showLoading();
    appSendSetCmd(type);
    this.hideLoadingTimer && clearTimeout(this.hideLoadingTimer);
    this.hideLoadingTimer = setTimeout(() => {
      mobile.hideLoading();
    }, delayHideLoadTime);
    this.intervalFetchRealData();
  };

  /**
   * @description: 三星二次确认弹框交互
   * @return {*}
   */
  onKeyButtonPress = (onConfirm = () => {}, onCancel = () => {}) => {
    mobile.simpleConfirmDialog(
      '',
      Strings.getLang('rn_4913_panelhome_keybuttonalerttitle_textview_text'),
      onConfirm,
      onCancel,
    );
  };
  /**
   * @description: 单机keybutton或者长按触发的方法
   * @param {Bool} value
   */
  onKeyButtonAction = value => {
    tracker.click({
      elementId: PANEL_HOME_PAGE_ELEMENTS_MAP.keySwitchButtonClick.id,
    });
    this.lockValue = value;
    this.actionWithType(CMD_DICTIONARY.SET_UNLOCK);
  };

  /**
   * @description: 处理倒车/日光灯/延时关灯事件
   */
  onSubmit = (type, index) => {
    switch (type) {
      case PANEL_ACTION.ALERT:
        tracker.click({
          elementId: PANEL_HOME_PAGE_ELEMENTS_MAP.reverseReminderButtonClick.id,
          expandInfo: CMD_DICTIONARY.SET_BACKUP + index
        });
        this.actionWithType(CMD_DICTIONARY.SET_BACKUP + index);
        break;
      case PANEL_ACTION.LIGHT:
        tracker.click({
          elementId: PANEL_HOME_PAGE_ELEMENTS_MAP.daytimeLightButtonClick.id,
          expandInfo: CMD_DICTIONARY.SET_DAYLIGHT + index
        });
        this.actionWithType(CMD_DICTIONARY.SET_DAYLIGHT + index);
        break;
      case PANEL_ACTION.DELAY:
        tracker.click({
          elementId:
            PANEL_HOME_PAGE_ELEMENTS_MAP.headlightDelayOffButtonClick.id,
            expandInfo: CMD_DICTIONARY.SET_DELAY_OFF + index
        });
        this.actionWithType(CMD_DICTIONARY.SET_DELAY_OFF + index);
        break;
      default:
        break;
    }
  };

  /**
   * @description: 获取设备背景大图
   */
  getBgImageSource = isLock => {
    return isLock
      ? require('@assets/z6/z6_4913_bg_car_gray.png')
      : require('@assets/z6/z6_4913_bg_car_normal.png');
  };

  /**
   * @description: 获取设备大图的marginTop
   */
  getMarginTopStyle = currentStatus => {
    if (DEVICE_WIDTH > 370) {
      return currentStatus === CURRENT_STATUS.MOWING
        ? {marginTop: -87 + 50}
        : {marginTop: -87 + 15};
    } else {
      // 适配某些窄屏手机320左右
      return currentStatus === CURRENT_STATUS.MOWING
        ? {marginTop: -87 + 30 + 75}
        : {marginTop: -87 + 15 + 75};
    }
  };

  /**
   * @description: 编辑设备名称点击事件
   */
  editDeviceNameAction = deviceDetail => {
    const {navigation} = this.props;
    navigation.navigate('ViewEditName', {
      defaultName: deviceDetail?.nickName
        ? deviceDetail?.nickName
        : this.props.panel.deviceName,
      callBack: () => {
        this.getDetailList();
      },
    });
  };

  /**
   * @description: 获取设备名称
   */
  getDeviceName = deviceDetail => {
    return deviceDetail?.nickName
      ? deviceDetail?.nickName
      : this.props.panel.deviceName;
  };

  /**
   * @description: 获取电池仓按钮Render
   */
  getBatterIconRender = (isLock, disabled) => {
    return isLock || disabled ? null : (
      <TouchableWithoutFeedback
        style={styles.batteryIconContainer}
        onPress={() => {
          tracker.click({
            elementId:
              PANEL_HOME_PAGE_ELEMENTS_MAP.batteryPackExpansionButtonClick.id,
          });
          const {showBatteryInfo} = this.state;
          this.setState({showBatteryInfo: !showBatteryInfo});
        }}>
        <MCVNIcon
          size={66}
          style={styles.batteryIconButton}
          source={require('@assets/z6/z6_btn_battery.png')}
        />
      </TouchableWithoutFeedback>
    );
  };

  /**
   * @description: 处理锁按钮点击事件
   */
  lockClickAction = value => {
    Vibration.vibrate();
    const brand = DeviceInfo.getBrand();
    // 单独处理三星交互效果
    if (brand === 'samsung') {
      this.onKeyButtonPress(
        () => {
          this.onKeyButtonAction(value);
        },
        () => {},
      );
    } else {
      this.onKeyButtonAction(value);
    }
  };

  /**
   * @description: 处理页面左侧点击
   */
  handlePageLeftClick = () => {
    this.dealWithGoBack();
  };

  /**
   * @description: 处理页面右侧点击
   */
  handlePageRightClick = () => {
    const {navigation} = this.props;
    tracker.click({
      elementId: PANEL_HOME_PAGE_ELEMENTS_MAP.deviceDetailsButtonClick.id,
    });
    navigation.navigate('ViewDetailList');
  };

  /**
   * @description: keyButton长按事件
   */
  handleOnLongPress = value => {
    const brand = DeviceInfo.getBrand();
    if (brand === 'samsung') {
      return;
    }
    this.onKeyButtonAction(value);
  };

  /**
   * @description: keyButton点击事件
   */
  handlekeyButtonPress = value => {
    this.lockClickAction(value);
  };

  /**
   * @description: 获取空白区域
   */
  whiteSpaceRender = () => {
    return <WhiteSpace size={isIphoneX ? 34 : 0} />;
  };

  render() {
    const {bleConnected, backUpAlertStatus, daytimeLightStatus, isLock} =
      this.props.panel;
    const {currentStatus = '', deviceDetail = {}} = this.props.panel.home;
    const disabled = !bleConnected;
    const rightElement = (
      <View style={styles.rightBack}>
        <Image source={require('@assets/z6/z6_icon_more.png')} />
      </View>
    );
    const titleElement = (
      <TouchableOpacity
        style={styles.titleView}
        onPress={() => {
          tracker.click({
            elementId: PANEL_HOME_PAGE_ELEMENTS_MAP.deviceNameClick.id,
          });
          this.editDeviceNameAction(deviceDetail);
        }}>
        <Text numberOfLines={1} style={styles.deviceName}>
          {this.getDeviceName(deviceDetail)}
        </Text>
        <Text style={styles.connectStatus}>{this.state.connectDesc}</Text>
      </TouchableOpacity>
    );
    const PanelHome = (
      <PageView>
        <HeaderView
          style={styles.navHeader}
          onLeftPress={this.handlePageLeftClick}
          rightElement={rightElement}
          onRightPress={this.handlePageRightClick}
          title={titleElement}
        />
        <ScrollView showsVerticalScrollIndicator={false}>
          <LinearGradient
            style={[styles.bg]}
            start={{x: 0, y: 0}}
            end={{x: 0, y: 1}}
            colors={['#FFFFFF', '#f7f7f7']}
          />
          <View style={[styles.container, styles.dashBoardTop]}>
            <Dashboard connected={bleConnected} />
          </View>
          {/* 设备主图及操作 */}
          <MCVNIcon
            style={[
              styles.deviceBackIcon,
              this.getMarginTopStyle(currentStatus),
            ]}
            source={this.getBgImageSource(isLock)}>
            {this.getBatterIconRender(isLock, disabled)}
            <KeyButton
              hidden={!bleConnected}
              isLock={isLock}
              onPress={this.handlekeyButtonPress}
              onLongPress={this.handleOnLongPress}
            />
          </MCVNIcon>
          {/* 电池仓card */}
          {this.state.showBatteryInfo ? (
            <Card
              cStyle={styles.batteryBttom}
              title={Strings.getLang(
                'rn_4913_batterydetail_title_textview_text',
              )}
              icon={require('@assets/z6/z6_icon_battery.png')}>
              <BatteryView />
            </Card>
          ) : null}
          <View style={[styles.container]}>
            <FaultView />
            <CardList />
          </View>
          {/* 倒车报警Card */}
          <View>
            <Card
              cStyle={styles.backAlertBox}
              disabled={disabled}
              title={Strings.getLang(
                'rn_4913_panelhome_backupalert_textview_text',
              )}
              titleStyle={styles.cardTitle}
              icon={require('@assets/z6/z6_icon_alarm.png')}>
              <SegmentView
                selectIndex={backUpAlertStatus}
                key="alarm"
                data={BACK_UP_ALERT_ARRAY}
                onItemSelect={(item, index) => {
                  this.onSubmit(PANEL_ACTION.ALERT, index);
                }}
              />
            </Card>
          </View>
          {/* 日行灯Card */}
          <Card
            cStyle={styles.backAlertBox}
            disabled={disabled}
            title={Strings.getLang(
              'rn_4913_panelhome_daytimelight_textview_text',
            )}
            titleStyle={styles.cardTitle}
            icon={require('@assets/z6/z6_icon_daytime_light.png')}>
            <SegmentView
              key="daytime_ligh"
              selectIndex={daytimeLightStatus}
              data={DAYTIME_LIGHT_ARRAY}
              onItemSelect={(item, index) => {
                this.onSubmit(PANEL_ACTION.LIGHT, index);
              }}
            />
          </Card>
          {/* 延时关灯Card */}

          <Card
            cStyle={styles.lightDelayBox}
            disabled={disabled}
            title={Strings.getLang(
              'rn_4913_panelhome_lightdelay_textview_text',
            )}
            titleStyle={styles.cardTitle}
            subTitle={`${Strings.getLang(
              'rn_4913_panelhome_willturnoff_textview_text',
            )} ${this.state.lightDelayOffTimeText}s`}
            icon={require('@assets/z6/z6_icon_lightoff_delay.png')}>
            {this.props.panel.realTimeA1.hasOwnProperty(
              'lightDelayOffTime',
            ) && (
              <LightDelaySlider
                style={styles.sliderWrapper}
                onSlidingComplete={index => {
                  this.onSubmit(PANEL_ACTION.DELAY, index);
                }}
                delayOffTimeTextChange={value => {
                  this.setState({lightDelayOffTimeText: value});
                }}
              />
            )}
            {this.props.panel.realTimeA1.hasOwnProperty(
              'lightDelayOffTime',
            ) ? null : (
              <LightDelayFreezeSlider style={styles.sliderWrapper} />
            )}
          </Card>

          {this.whiteSpaceRender()}
        </ScrollView>
      </PageView>
    );

    return PanelHome;
  }
}

const styles = StyleSheet.create({
  navHeader: {
    backgroundColor: '#ffffff',
  },
  titleView: {
    alignItems: 'center',
  },
  rightBack: {
    width: 40,
    height: 40,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    marginRight: -20,
    paddingRight: 20,
  },
  deviceName: {
    fontSize: 19,
    color: '#020202',
    fontWeight: 'bold',
  },
  connectStatus: {
    fontSize: 14,
    color: '#999999',
  },
  deviceBackIcon: {
    width: DEVICE_WIDTH,
    height: (DEVICE_WIDTH / 15) * 11,
    alignSelf: 'center',
    justifyContent: 'space-between',
    position: 'relative',
  },
  backAlertBox: {
    marginHorizontal: 14,
    marginBottom: 13,
    height: 151,
  },

  lightDelayBox: {
    marginHorizontal: 14,
    height: 160,
    marginBottom: 13,
  },
  sliderWrapper: {
    marginTop: 10,
  },
  cardTitle: {
    color: '#000000',
    fontSize: 17,
    fontWeight: '500',
    marginLeft: 10,
  },
  batteryIconContainer: {
    width: 120,
    height: 120,
    alignSelf: 'flex-end',
  },
  batteryIconButton: {
    alignSelf: 'flex-end',
    marginRight: 30,
    marginTop: 30,
  },
  container: {
    paddingBottom: 13,
    paddingHorizontal: 14,
  },
  moving: {
    color: '#77BC1F',
    fontSize: 12,
    lineHeight: 16.5,
    textAlign: 'center',
    marginTop: 2,
  },
  bg: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 300,
    zIndex: -1,
  },
  dashBoardTop: {
    paddingTop: 13.5,
  },
  batteryBttom: {
    marginBottom: 13,
  },
});
