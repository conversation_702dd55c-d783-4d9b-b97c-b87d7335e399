/*
 * @Author: <EMAIL>
 * @Date: 2023-11-17 10:21:14
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2024-01-25 15:33:28
 * @FilePath: /Z6/src/pages/detail/EditName.js
 * @Description: 设备名字修改页
 *
 */

import React, {Component} from 'react';
import {Text, View, StyleSheet, ScrollView, TextInput} from 'react-native';
import {inject, observer} from 'mobx-react/native';
import Strings from '@i18n';
import {Button} from 'react-native-elements';
import {mobile, PageView} from 'cvn-panel-kit';
import {HeaderView} from '@components';
import tracker, {EDIT_PAGE_ELEMENTS_MAP} from '@tracking';

@inject('panelActions', 'panel')
@observer
export default class EditName extends Component {
  constructor(props) {
    super(props);
    const {route} = this.props;
    const defaultName = route.params.defaultName;
    this.callBack = route.params.callBack;
    this.state = {
      name: defaultName,
      count: defaultName?.length || 0,
    };
  }
  onSave = () => {
    const {navigation} = this.props;
    tracker.click({elementId: EDIT_PAGE_ELEMENTS_MAP.confirmButtonClick.id});
    const {name} = this.state;
    if (validateDeviceName(name) === false) {
      const title = Strings.getLang(
        'rn_common_deviceeditname_desc_textview_text',
      );
      mobile.toast(title, () => {});
      return;
    }
    this.props.panelActions
      .deviceEdit({
        deviceId: this.props.panel.deviceId,
        deviceNickname: name,
      })
      .then(res => {
        mobile.toast(
          Strings.getLang('rn_common_operation_success_textview_text'),
          () => {},
        );
        // 刷新列表
        this.callBack();
        navigation.goBack();
      });
  };
  render() {
    const {name, count} = this.state;
    const {navigation} = this.props;
    return (
      <PageView>
        <HeaderView
          title={Strings.getLang(
            'rn_common_deviceeditname_title_textview_text',
          )}
          useCommonEleId={false}
          onLeftPress={() => {
            navigation.goBack();
          }}
        />
        <ScrollView>
          <View style={styles.inputBox}>
            <TextInput
              maxLength={20}
              style={styles.itemText}
              placeholder={Strings.getLang(
                'rn_common_deviceeditname_placeholder_input_text',
              )}
              defaultValue={name}
              value={name}
              onChangeText={v => {
                const value = v;
                this.setState({name: value, count: value?.length || 0});
              }}
            />
            <Text style={styles.rightText}>{`${count}/20`}</Text>
          </View>
          <Text style={styles.descText}>
            {Strings.getLang('rn_common_deviceeditname_desc_textview_text')}
          </Text>
          <Button
            onPress={this.onSave}
            title={Strings.getLang('rn_common_deviceeditname_save_button_text')}
            containerStyle={styles.buttonContaner}
            buttonStyle={styles.button}
            titleStyle={styles.title}
          />
        </ScrollView>
      </PageView>
    );
  }
}

const styles = StyleSheet.create({
  inputBox: {
    marginTop: 10,
    height: 50,
    borderRadius: 5,
    marginHorizontal: 10,
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    justifyContent: 'space-between',
  },
  itemText: {
    fontSize: 16,
    color: '#000000',
    backgroundColor: '#ffffff',
    height: '100%',
    width: '85%',
  },
  rightText: {
    fontSize: 15,
    color: '#999999',
  },
  descText: {
    fontSize: 15,
    color: '#999999',
    marginHorizontal: 25,
    marginTop: 10,
  },
  buttonContaner: {
    marginHorizontal: 15,
    height: 48,
    marginTop: 30,
  },
  button: {
    backgroundColor: '#77BC1F',
    borderRadius: 2,
    height: 48,
  },
  title: {
    color: '#FFFFFF',
    fontSize: 18,
  },
});

// 最新规则
export const validateDeviceName = name => {
  return /^.{1,20}$/.test(name);
};
