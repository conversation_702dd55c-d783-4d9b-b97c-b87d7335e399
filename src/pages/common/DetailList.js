/*
 * @Author: <EMAIL>
 * @Date: 2023-11-17 10:21:14
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2024-05-24 11:50:59
 * @FilePath: /Z6/src/pages/detail/List.js
 * @Description: 设备详情列表页
 *
 */
import React, {Component} from 'react';
import {Text, View, ScrollView, StyleSheet} from 'react-native';
import {inject, observer} from 'mobx-react/native';
import {
  mobile,
  CommonEmitter,
  Utils,
  WhiteSpace,
  PageView,
} from 'cvn-panel-kit';
import {HeaderView, Item} from '@components';
import {Button} from 'react-native-elements';
import {isIphoneX, DEVICE_WIDTH} from '@utils/device';
import Strings from '@i18n';
import PropTypes from 'prop-types';
import tracker, {
  DETAIL_LIST_PAGE_ELEMENTS_MAP,
  REMOVE_DEVICE_PAGE_ELEMENTS_MAP,
  REMOVE_DEVICE_ALERT_DIALOG_PAGE_ID,
} from '@tracking';
const {JumpUtils} = Utils;

// 设备名 key
const commonDeviceName = 'rn_common_detaillist_devicename_textview_text';
// 注册信息 key
const commonRegistration = 'rn_common_detaillist_registration_textview_text';
// 设备关于info key
const commonAbout = 'rn_common_detaillist_about_textview_text';
// 产品介绍 key
const commonProductIntro = 'rn_common_detaillist_productintro_textview_text';
// 配件 key
const commonParts = 'rn_common_detaillist_parts_textview_text';
// 统计 key
const commonStatistics = 'rn_common_detaillist_statistics_textview_text';
// 隐私 key
const commonPrivacy = 'rn_common_detaillist_privacy_textview_text';

@inject('panelActions', 'panel')
@observer
export default class List extends Component {
  constructor(props) {
    super(props);

    const {region} = this.props.panel;

    const euBaseData = [
      {
        icon: require('@assets/z6/z6_icon_name.png'),
        title: Strings.getLang(commonDeviceName),
        key: commonDeviceName,
      },
      {
        icon: require('@assets/z6/z6_icon_regis.png'),
        title: Strings.getLang(commonRegistration),
        key: commonRegistration,
      },
      {
        icon: require('@assets/z6/z6_icon_infor.png'),
        title: Strings.getLang(commonAbout),
        key: commonAbout,
      },
    ];
    const naBaseData = [
      {
        icon: require('@assets/z6/z6_icon_name.png'),
        title: Strings.getLang(commonDeviceName),
        key: commonDeviceName,
      },
      {
        icon: require('@assets/z6/z6_icon_regis.png'),
        title: Strings.getLang(commonRegistration),
        key: commonRegistration,
      },
      {
        icon: require('@assets/z6/z6_icon_baike.png'),
        title: Strings.getLang(commonProductIntro),
        key: commonProductIntro,
      },
      {
        icon: require('@assets/z6/z6_icon_infor.png'),
        title: Strings.getLang(commonAbout),
        key: commonAbout,
      },
      {
        icon: require('@assets/z6/z6_icon_parts.png'),
        title: Strings.getLang(commonParts),
        key: commonParts,
      },
      {
        icon: require('@assets/common_icon_feedback.png'),
        title: Strings.getLang('rn_common_detaillist_feedback_textview_text'),
        key: 'feedback',
      },
    ];
    if (region === 'EU') {
      this.baseData = euBaseData;
    } else {
      this.baseData = naBaseData;
    }
  }

  componentDidMount() {
    CommonEmitter.addListener('NAVIGATOR_ON_WILL_FOCUS', () => {
      this.getDetailList();
    });
    this.getDetailList();
  }

  componentWillUnmount() {
    CommonEmitter.removeAllListeners('NAVIGATOR_ON_WILL_FOCUS');
  }

  /**
   * @description: 设备详情信息获取
   */
  getDetailList = () => {
    this.props.panelActions.deviceDetail({
      req: this.props.panel.deviceId,
    });
  };
  /**
   * @description: 解绑设备
   */
  unBindDevice = () => {
    tracker.click({
      elementId: DETAIL_LIST_PAGE_ELEMENTS_MAP.deleteDeviceButtonClick.id,
    });
    tracker.openPopup({
      pageId: REMOVE_DEVICE_ALERT_DIALOG_PAGE_ID,
      elementId: REMOVE_DEVICE_PAGE_ELEMENTS_MAP.exposure.id,
    });
    mobile.simpleConfirmDialog(
      Strings.getLang('rn_common_detaillist_alerttitle_textview_text'),
      Strings.getLang('rn_common_detaillist_alertmessage_textview_text'),
      () => {
        tracker.click({
          pageId: REMOVE_DEVICE_ALERT_DIALOG_PAGE_ID,
          elementId: REMOVE_DEVICE_PAGE_ELEMENTS_MAP.confirmButtonClick.id,
        });
        this.props.panelActions
          .deviceUnbind({
            deviceId: this.props.panel.deviceId,
          })
          .then(res => {
            tracker.click({
              pageId: REMOVE_DEVICE_ALERT_DIALOG_PAGE_ID,
              elementId: REMOVE_DEVICE_PAGE_ELEMENTS_MAP.removeDeviceSuccess.id,
            });
            tracker.closePopup({
              pageId: REMOVE_DEVICE_ALERT_DIALOG_PAGE_ID,
              elementId: REMOVE_DEVICE_PAGE_ELEMENTS_MAP.stayDuration.id,
            });
            // 重连停止标识
            mobile.back();
          })
          .catch(error => {
            tracker.click({
              pageId: REMOVE_DEVICE_ALERT_DIALOG_PAGE_ID,
              elementId: REMOVE_DEVICE_PAGE_ELEMENTS_MAP.removeDeviceFail.id,
            });
          });
      },
      () => {
        tracker.click({
          pageId: REMOVE_DEVICE_ALERT_DIALOG_PAGE_ID,
          elementId: REMOVE_DEVICE_PAGE_ELEMENTS_MAP.cancelButtonClick.id,
        });
        tracker.closePopup({
          pageId: REMOVE_DEVICE_ALERT_DIALOG_PAGE_ID,
          elementId: REMOVE_DEVICE_PAGE_ELEMENTS_MAP.stayDuration.id,
        });
      },
    );
  };

  render() {
    const {navigation} = this.props;
    const {showRed = false, deviceDetail: detail} = this.props.panel;
    const nickName = detail?.nickName ? detail?.nickName : detail?.deviceName;
    return (
      <PageView>
        <HeaderView
          title={Strings.getLang('rn_common_detaillist_title_textview_text')}
          onLeftPress={() => {
            tracker.click({
              elementId: DETAIL_LIST_PAGE_ELEMENTS_MAP.returnButtonClick.id,
            });
            navigation.goBack();
          }}
        />
        <ScrollView showsVerticalScrollIndicator={false}>
          <BaseCard
            data={this.baseData}
            showRed={showRed}
            name={nickName}
            hasRegisted={detail?.infoStatus === 1}
            onItemClicked={item => {
              switch (item.key) {
                case commonAbout:
                  tracker.click({
                    elementId:
                      DETAIL_LIST_PAGE_ELEMENTS_MAP
                        .equipentInformationButtonClick.id,
                  });
                  navigation.navigate('ViewDeviceMsg', {detail});
                  break;
                case commonProductIntro:
                  {
                    tracker.click({
                      elementId:
                        DETAIL_LIST_PAGE_ELEMENTS_MAP
                          .productEncyclopediaButtonClick.id,
                    });
                    const {productId} = this.props.panel;
                    const route = 'ChervonIot://EGO/DeviceManage/productHelp';
                    const params = {
                      productId,
                    };
                    JumpUtils.jumpTo(route, params);
                  }
                  break;
                case commonParts:
                  {
                    tracker.click({
                      elementId:
                        DETAIL_LIST_PAGE_ELEMENTS_MAP.enclosureButtonClick.id,
                    });
                    const {deviceId, productId} = this.props.panel;
                    const route =
                      'ChervonIot://EGO/DeviceManage/productFittings';
                    const params = {
                      deviceId,
                      productId,
                    };
                    JumpUtils.jumpTo(route, params);
                  }
                  break;
                case commonDeviceName: {
                  tracker.click({
                    elementId:
                      DETAIL_LIST_PAGE_ELEMENTS_MAP.equipmentNameButtonClick.id,
                  });
                  navigation.navigate('ViewEditName', {
                    defaultName: nickName,
                    callBack: () => {
                      this.getDetailList();
                    },
                  });
                  break;
                }
                case commonRegistration:
                  tracker.click({
                    elementId:
                      DETAIL_LIST_PAGE_ELEMENTS_MAP
                        .deviceRegistrationButtonClick.id,
                  });
                  JumpUtils.jumpToRegistration(
                    detail,
                    this.props.panel.deviceId,
                  );
                  break;
                case commonStatistics:
                  break;
                case commonPrivacy:
                  JumpUtils.jumpToPrivacy(this.props.panel);
                  break;
                case 'feedback': {
                  tracker.click({
                    elementId:
                      DETAIL_LIST_PAGE_ELEMENTS_MAP.feedbackButtonClick.id,
                  });
                  const {productId, deviceId} = this.props.panel;
                  const {commodityModel, sn} = detail;
                  JumpUtils.jumpTo(
                    'ChervonIot://EGO/UserCenter/feedbackPublish',
                    {
                      sourceType: 'RN',
                      productId,
                      deviceId,
                      nickName,
                      commodityModel,
                      sn,
                    },
                  );
                  break;
                }

                default:
              }
            }}
          />
          <Button
            onPress={this.unBindDevice}
            title={Strings.getLang(
              'rn_common_detaillist_deletedevice_button_text',
            )}
            containerStyle={styles.buttonContaner}
            buttonStyle={styles.buttonStyle}
            titleStyle={styles.titleStyle0}
          />
          <WhiteSpace size={isIphoneX ? 34 : 10} />
        </ScrollView>
      </PageView>
    );
  }
}
export const BaseCard = ({
  name = '',
  hasRegisted = false,
  onItemClicked = () => {},
  showRed = false,
  data = [],
}) => {
  const slicedName = name;
  return (
    <View style={styles.featureContainer}>
      {data.map((item, index) => {
        let rightExtra = <Text style={styles.subTitle} />;
        if (item?.key === commonDeviceName) {
          rightExtra = (
            <Text
              numberOfLines={1}
              style={[styles.subTitle, styles.deviceName]}>
              {slicedName}
            </Text>
          );
        }
        if (item?.key === commonRegistration) {
          if (hasRegisted) {
            rightExtra = (
              <Text style={styles.subTitle}>
                {Strings.getLang(
                  'rn_common_detaillist_registered_textview_text',
                )}
              </Text>
            );
          } else {
            rightExtra = (
              <Text style={styles.subTitle}>
                {Strings.getLang(
                  'rn_common_detaillist_unregistered_textview_text',
                )}
              </Text>
            );
          }
        }
        return (
          <Item
            titleStyle={styles.titleStyle}
            leftIconSource={item.icon}
            onPress={() => {
              onItemClicked(item, index);
            }}
            hideLine={index === data.length - 1}
            key={item.title}
            title={item.title}
            rightExtra={rightExtra}
          />
        );
      })}
    </View>
  );
};
BaseCard.propTypes = {
  name: PropTypes.string,
  hasRegisted: PropTypes.bool,
  onItemClicked: PropTypes.func,
  showRed: PropTypes.bool,
  data: PropTypes.array,
};
const styles = StyleSheet.create({
  title: {
    marginTop: 14.5,
    marginLeft: 19,
    fontSize: 12,
    color: '#999999',
  },
  heading: {
    color: 'white',
    fontSize: 22,
    fontWeight: 'bold',
  },
  headerLeft: {
    display: 'flex',
    flexDirection: 'row',
    marginTop: 5,
  },
  headerRight: {
    display: 'flex',
    flexDirection: 'row',
    marginTop: 5,
  },
  featureContainer: {
    marginTop: 10,
    borderRadius: 10,
    marginHorizontal: 15,
    overflow: 'hidden',
  },
  deviceName: {
    width: DEVICE_WIDTH - 135 - 66 - 30,
    textAlign: 'right',
  },
  subTitle: {
    color: '#666666',
    fontSize: 13,
  },
  registerText: {
    color: '#77BC1F',
    fontSize: 13,
  },
  redDot: {
    borderRadius: 6.5,
    width: 13,
    height: 13,
    backgroundColor: '#EC6464',
  },
  titleStyle: {
    marginLeft: 0,
  },
  buttonStyle: {
    backgroundColor: '#ffffff',
    borderRadius: 2,
    justifyContent: 'flex-start',
    paddingLeft: 23,
    height: 50,
  },
  titleStyle0: {
    color: '#77BC1F',
    fontSize: 15,
  },

  buttonContaner: {
    marginHorizontal: 15,
    height: 48,
    marginTop: 10,
    borderRadius: 10,
  },
});
