/*
 * @Author: <EMAIL>
 * @Date: 2023-11-17 10:21:14
 * @LastEditors: cdlsp
 * @LastEditTime: 2024-04-26 18:33:31
 * @FilePath: /chervonreactnative/Z6/src/pages/detail/DeviceMsg.js
 * @Description: 设备信息关于页
 */

import React, {Component} from 'react';
import {Text, View, StyleSheet, ScrollView} from 'react-native';
import {PageView, mobile} from 'cvn-panel-kit';
import {Item, HeaderView} from '@components';
import Clipboard from '@react-native-clipboard/clipboard';
import {inject, observer} from 'mobx-react/native';
import Strings from '@i18n';
import PropTypes from 'prop-types';
import pkg from 'IOTRN/package.json';
import tracker, {ABOUT_PAGE_ELEMENTS_MAP} from '@tracking';

// model号码
const modelNokey = 'MODEL NO.';
// 序列号number
const serialNumberkey = 'Serial number';
// 设备id
const deviceIdkey = 'Device ID';
// 当前运行的RN业务包版本
const rnVersionkey = 'RN Version';

@inject('panelActions', 'panel')
@observer
export default class DeviceMsg extends Component {
  constructor(props) {
    super(props);
    const {route} = this.props;
    const detail = route.params.detail || {};

    const tmpDetail = {...detail};
    const outData = [
      {
        title: Strings.getLang('rn_common_devicemsg_modelno_textview_text'),
        key: modelNokey,
      },
      {
        title: Strings.getLang('rn_common_devicemsg_sn_textview_text'),
        key: serialNumberkey,
      },
      {
        title: Strings.getLang('rn_common_devicemsg_deviceid_textview_text'),
        key: deviceIdkey,
      },
      {
        title: Strings.getLang('rn_common_devicemsg_rnversion_textview_text'),
        key: rnVersionkey,
      },
    ];
    const tmpData = outData.map(item => {
      const obj = {...item};
      switch (item.key) {
        case modelNokey:
          obj.content = tmpDetail.commodityModel;
          break;
        case serialNumberkey:
          obj.content = tmpDetail.sn;
          break;
        case deviceIdkey:
          obj.content = tmpDetail.deviceId;
          break;
        case rnVersionkey:
          obj.content = pkg.version;
          break;
        default:
          break;
      }

      return obj;
    });
    this.state = {
      detail: {
        sn: '',
        nickName: '',
      },
      data: tmpData,
    };
  }
  render() {
    const {navigation} = this.props;
    const {data} = this.state;
    return (
      <PageView>
        <HeaderView
          title={Strings.getLang('rn_common_devicemsg_title_textview_text')}
          onLeftPress={() => {
            tracker.click({
              elementId: ABOUT_PAGE_ELEMENTS_MAP.returnButtonClick.id,
            });
            navigation.goBack();
          }}
        />
        <ScrollView contentContainerStyle={styles.topPadding}>
          <Card data={data} />
        </ScrollView>
      </PageView>
    );
  }
}

export const Card = ({data = []}) => {
  return (
    <View style={styles.cardBox}>
      {data.map((item, index) => {
        return (
          <Item
            iconShow={false}
            hideLine={index === data.length - 1}
            lineStyle={styles.lineStyle}
            key={item.key}
            title={item.title}
            rightElement={<Text style={styles.rightText}>{item.content}</Text>}
            onPress={() => {
              Clipboard.setString(item.content ?? '');
              mobile.toast(
                Strings.getLang(
                  'rn_common_devicemsg_copiedtoast_textview_text',
                ),
                () => {},
              );
            }}
          />
        );
      })}
    </View>
  );
};
Card.propTypes = {
  data: PropTypes.array,
};
const styles = StyleSheet.create({
  cardBox: {
    backgroundColor: '#ffffff',
  },
  rightText: {
    color: '#999999',
    fontSize: 12,
  },
  topPadding: {
    paddingTop: 15,
  },
  lineStyle: {
    marginLeft: 20,
    marginRight: 16,
  },
});
