/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-10-12 14:04:33
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2024-01-25 19:35:22
 * @FilePath: /Z6/src/pages/stacks/HomeStackScreen.js
 * @Description: home栈
 */
import React from 'react';
import PropTypes from 'prop-types';
import {createStackNavigator} from '@react-navigation/stack';
import {inject, observer} from 'mobx-react/native';
import Routes from '../Route';

const HomeStack = createStackNavigator();

@inject('panelActions')
@observer
export default class HomeStackScreen extends React.Component {
  static propTypes = {
    panelActions: PropTypes.shape({
      initParams: PropTypes.func,
    }),
  };

  constructor(props) {
    super(props);
    this.props.panelActions.initParams({...props});
  }

  render() {
    const names = Object.keys(Routes);
    // StackNavigator栈的个数暂时和tab数量一致
    return (
      <HomeStack.Navigator
        screenOptions={({route, navigation}) => {
          return {
            headerShown: false,
            // 可以控制手势返回，暂时全部关闭
            gestureEnabled: false,
          };
        }}>
        {names.map(name => {
          const Component = Routes[name];
          return (
            <HomeStack.Screen
              key={name}
              name={name}
              component={Component}
              options={{title: name}}
            />
          );
        })}
      </HomeStack.Navigator>
    );
  }
}
