/*
 * @Author: <EMAIL>
 * @Date: 2023-11-17 10:21:14
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2024-01-30 16:49:58
 * @FilePath: /Z6/src/components/Item.js
 * @Description:
 *
 */
import React from 'react';
import {Item} from 'cvn-panel-kit';

/**
 * 下划线
 */
const ItemView = ({...res}) => {
  return (
    <Item
      rightIconSource={require('@assets/z6/z6_icon_arrow_right.png')}
      {...res}
    />
  );
};
export {ItemView as Item};
export default ItemView;
