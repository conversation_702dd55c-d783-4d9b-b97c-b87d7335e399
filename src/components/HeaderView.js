/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-02-09 11:35:30
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2024-01-30 16:30:22
 * @FilePath: /Z6/src/components/HeaderView.js
 * @Description: 公共Header二次封装
 */
import React from 'react';
import {HeaderView} from 'cvn-panel-kit';

/**
 * 下划线
 */
const Header = ({...res}) => {
  const {onLeftPress = () => {}} = res;
  return (
    <HeaderView
      backIconSource={require('@assets/z6/z6_icon_arrow_left_new.png')}
      {...res}
      onLeftPress={() => {
        onLeftPress();
      }}
    />
  );
};
export {Header as HeaderView};
export default Header;
