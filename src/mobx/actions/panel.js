/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-01-10 17:02:29
 * @LastEditors: cdlsp
 * @LastEditTime: 2024-04-26 17:41:35
 * @FilePath: /chervonreactnative/Z6/src/mobx/actions/panel.js
 * @Description: mobx 对action 封装
 */
import {action} from 'mobx';
import apiMap from 'src/api/ApiMap.js';
import {CVNSdk, device} from 'cvn-panel-kit';
import {Platform} from 'react-native';
import topicMap from 'src/utils/topic.js';
import {isEqual} from 'lodash-es';
const leftWalk = '8060'; // 左行走
const leftWalkNoError = '100100'; // 左行走无故障
const rightWalk = '8061'; // 右行走
const rightWalkNoError = '100200'; // 右行走无故障
const leftMower = '8062'; // 右割草
const leftNoError = '100300'; // 右割草无故障
const rightMower = '8063'; // 左割草
const rightNoError = '100400'; // 左割草无故障
const bmsPoint = '8064'; // bms
const bmsNoError = '100500'; // bms无故障
const switchPoint = '8065'; // 开关
const switchNoError = '100600'; // 开关无故障
const parkPoint = '8066'; // 驻车位置
const parkNoError = '100700'; // 驻车无故障

export default class PanelActions {
  constructor({panel}) {
    this.panel = panel;
  }

  /**
   * @description: 设备详情 暴露接口
   * @param {Object} params 请求参数
   * @return {Promise} 用于外部primise调用
   */
  @action deviceDetail = params => {
    this.getDeviceDetail(params).then(res => {
      this.setDetail(res?.entry);
    });
  };

  /**
   * @description: 实际调用设备详情接口
   * @param {Object} params 请求参数
   * @return {Promise} 用于外部primise调用
   */
  @action getDeviceDetail = params => {
    return CVNSdk.apiRequest(apiMap.deviceDetail, params);
  };

  /**
   * @description: 获取设备外设信息详情个数相关
   * @description: 设备外设详情 暴露接口
   * @param {Object} params 请求参数
   * @return {Promise} 用于外部primise调用
   */
  @action deviceAccessories = params => {
    this.getDeviceAccessories(params).then(res => {
      this.setDievceAccessories(res?.entry);
    });
  };

  /**
   * @description: 获取设备外设详情接口
   * @param {Object} params 请求参数
   * @return {Promise} 用于外部primise调用
   */
  @action getDeviceAccessories = params => {
    return CVNSdk.apiRequest(apiMap.getPartsOverview, params);
  };

  /**
   * @description: 设备解绑
   * @param {Object} params 请求参数
   * @return {Promise} 用于外部primise调用
   */
  @action deviceUnbind = params => {
    return CVNSdk.apiRequest(apiMap.deviceUnbind, params);
  };

  /**
   * @description: 设备编辑
   * @param {Object} params 请求参数
   * @return {Promise} 用于外部primise调用
   */
  @action deviceEdit = params => {
    return CVNSdk.apiRequest(apiMap.deviceEdit, params);
  };

  /**
   * @description: 原生传递参数后初始化
   * @param {Object} params 初始化对象
   */
  @action initParams = params => {
    this.panel.initialParams = params;
  };

  /**
   * @description: 设置详情数据
   * @param {Object} params 设备详情对象
   */
  @action setDetail = params => {
    this.panel.deviceDetail = params;
  };

  /**
   * @description: 设置设备外设数量
   * @param {Object} obj 设备外设详情
   */
  @action setDievceAccessories = obj => {
    this.panel.accessories = obj;
  };

  /**
   * @description: 消除故障消息数据
   * @param {Object} messageData 消息数据
   */
  @action clearErrorData = messageData => {
    const errorMap = {
      [leftWalk]: leftWalkNoError,
      [rightWalk]: rightWalkNoError,
      [leftMower]: leftNoError,
      [rightMower]: rightNoError,
      [bmsPoint]: bmsNoError,
      [switchPoint]: switchNoError,
      [parkPoint]: parkNoError,
    };
    const keys = Object.keys(errorMap);
    const values = Object.values(messageData);
    const messageString = JSON.stringify(values);
    for (const key of keys) {
      if (
        messageData.hasOwnProperty(key) &&
        messageString.indexOf(errorMap[key]) !== -1
      ) {
        this.panel.messageDataArray = [];
        return;
      }
    }
  };

  /**
   * @description: 消息数据设置
   * @param {Object} messageData 消息数据
   */
  @action setMessageData = messageData => {
    this.panel.messageDataArray = [messageData];
  };

  /**
   * @description: 设置蓝牙连接
   * @param {Boalean} value
   */
  @action setBleConnected = value => {
    this.panel.bleConnected = value;
  };
  /**
   * @description: 判断是否在RNContainerVC内
   * @param {Boolean} value
   */
  @action setIsInRNContainerVC = value => {
    this.panel.isInRNContainerVC = !!value;
  };

  /**
   * @description: 设置初始数据源
   * @param {Object} value
   */
  @action setRealTimeData = (value = {}) => {
    const {
      realTimeA0 = {},
      realTimeA1 = {},
      parseResult = {},
      backUpDataSource = {},
      dayLightDataSource = {},
      numberLightDelayOffLevel = 0,
      isLockDataSource = false,
      batteryParseResult = {},
    } = value;

    if (!isEqual(batteryParseResult, this.panel.batteryParseResult)) {
      this.panel.batteryParseResult = {
        ...this.panel.batteryParseResult,
        ...batteryParseResult,
      };
    }
    if (!isEqual(realTimeA0, this.panel.realTimeA0)) {
      this.panel.realTimeA0 = {...this.panel.realTimeA0, ...realTimeA0};
    }

    if (!isEqual(realTimeA1, this.panel.realTimeA1)) {
      this.panel.realTimeA1 = {...this.panel.realTimeA1, ...realTimeA1};
    }

    if (!isEqual(parseResult, this.panel.parseResult)) {
      this.panel.parseResult = {...this.panel.parseResult, ...parseResult};
    }
    if (!isEqual(backUpDataSource, this.panel.backUpDataSource)) {
      this.panel.backUpDataSource = {
        ...this.panel.backUpDataSource,
        ...backUpDataSource,
      };
    }
    if (!isEqual(dayLightDataSource, this.panel.dayLightDataSource)) {
      this.panel.dayLightDataSource = {
        ...this.panel.dayLightDataSource,
        ...dayLightDataSource,
      };
    }

    if (this.panel.numberLightDelayOffLevel !== numberLightDelayOffLevel) {
      this.panel.numberLightDelayOffLevel = numberLightDelayOffLevel;
    }

    if (this.panel.isLockDataSource !== isLockDataSource) {
      this.panel.isLockDataSource = isLockDataSource;
    }
  };

  /**
   * @description: 设置16进制字符串DeviceId,取自蓝牙鉴权指令
   * @param {String} value
   */
  @action setHexStringDeviceId = (values = undefined) => {
    this.panel.hexStringDeviceId = values;
  };

  /**
   * topic订阅
   */
  @action subscribeWithTopic = () => {
    const {deviceId} = this.panel;
    if (device.subscribe) {
      const shadowUpdateAcceptedStr = `${topicMap.preFix}${deviceId}${topicMap.shadowUpdateAcceptedSuffix}`;
      let qos = 1;
      if (Platform.OS === 'ios') {
        qos = 2;
      }
      device.subscribeV2(shadowUpdateAcceptedStr, qos);
    }
  };
}
