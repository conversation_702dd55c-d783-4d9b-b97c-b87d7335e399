/*
 * @Author: <EMAIL>
 * @Date: 2023-12-20 17:58:49
 * @LastEditors: cdlsp
 * @LastEditTime: 2024-05-06 18:08:58
 * @FilePath: /chervonreactnative/Z6/src/mobx/store/panel.js
 * @Description: mobx数据源
 *
 */
import {observable, computed} from 'mobx';
import Strings from '@i18n';
import {TimeUtils} from 'cvn-panel-kit/src/utils';
import {CURRENT_STATUS} from 'IOTRN/src/utils/home';

export default class PanelStore {
  /**
   * @typedef {Object} initialParams
   * @description: app原生传入的初始数据
   */
  @observable initialParams = {};
  /**
   * @typedef {String} hexStringDeviceId
   * @description: 16进制设备id字符串
   */
  @observable hexStringDeviceId = '';
  /**
   * @typedef {Object} deviceDetail
   * @description: 设备详情数据
   */
  @observable deviceDetail = {};
  /**
   * @typedef {Boolean} bleConnected
   * @description: 蓝牙是否已经连接
   */
  @observable bleConnected = false;
  /**
   * @typedef {Boolean} isNativeGoback
   * @description: 调用了原生pop操作标识
   */
  @observable isNativeGoback = false;
  /**
   * @typedef {Boolean} isInRNContainerVC
   * @description: 是否在RN容器内
   */
  @observable isInRNContainerVC = true;
  /**
   * @typedef {Array} messageDataArray
   * @description: 原生监听回来后的设备消息体数组
   * @property {String} title - 错误信息
   */
  @observable messageDataArray = [];

  /**
   * @typedef {Object} realTimeData
   * @description: 实时数据的原始数据源
   */
  @observable realTimeData = {};

  /**
   * @typedef {Object} parseResult
   * @description:解析后的数据,面板数据源
   */
  @observable parseResult = {};

  /**
   * @typedef {Object} batteryDetail
   * @description:解析后的数据,面板电池相关数据源
   */
  @observable batteryParseResult = {};

  /**
   * @description:面板大灯延时关闭数据源
   * @typedef {Number} numberLightDelayOffLevel
   */
  @observable numberLightDelayOffLevel = 0;

  /**
   * @description:面板日光灯数据源
   * @typedef {Object} dayLightDataSource
   * @property {Boolean} isSetHorseLightOn - 跑马灯是否开启
   * @property {Number} isSetDayLightOn - 日光灯是否开启
   */
  @observable dayLightDataSource = {};

  /**
   * @description:面板车辆是否锁定数据源
   * @typedef {Boolean} isLockDataSource
   */
  @observable isLockDataSource = false;

  // 设备外设数量数据源
  @observable accessories = {};

  /**
   * @description:面板倒车提醒数据源
   * @typedef {Object} backUpDataSource
   * @property {Boolean} isSetBackLightOn - 倒车灯是否开启
   * @property {Number} numLightBrightnessGear - 灯光亮度档位
   */
  @observable backUpDataSource = {};

  /**
   * @description:实时数据A0区域数据集-用于设置命令数据组装
   * @typedef {Object} realTimeA0
   * @property {string} currentSet - 设置开关当前状态
   */
  realTimeA0 = {};

  /**
   * @description:实时数据A1区域数据集-用于设置命令数据组装
   * @typedef {Object} realTimeA1
   * @property {string} setedBladeRpmGear - 档位的刀片转速
   * @property {string} setedSpeedGear - 档位的行驶速度
   * @property {string} lightDelayOffTime - 光延时熄灭设定
   * @property {string} lightBrightnessGear - 灯光亮度设定档位
   * @property {string} lineRunCorrect - 直线行走矫正
   */
  realTimeA1 = {};

  /**
   * @description: home 电量显示
   * @return {String}
   */
  @computed get totalBatteryPercentValue() {
    const {totalBatteryValue} = this;
    const result = totalBatteryValue ? `${totalBatteryValue}` : '';
    return result;
  }
  /**
   * @description: 也用于仪表盘指标值 百分比电量显示
   * @return {String}
   */
  @computed get totalBatteryValue() {
    const {batteryParseResult} = this;
    const {numberBatteryValue} = batteryParseResult;
    const result = numberBatteryValue;
    return result;
  }

  /**
   * @description: home.js 统一在store中的数据源
   * @return {Object}
   */
  @computed get home() {
    const {
      runningSpeed,
      bladeRotation,
      remainChargingTime,
      remainRunningTime,
      currentStatus,
      deviceDetail,
      totalBatteryPercentValue,
    } = this;

    const result = {
      totalBatteryPercentValue,
      currentStatus,
      runningSpeed,
      bladeRotation,
      remainChargingTime,
      remainRunningTime,
      deviceDetail,
    };
    return result;
  }

  /**
   * @description: 故障相关信息
   * @return {String}
   */
  @computed get errorText() {
    if (this.messageDataArray.length > 0) {
      let {title} = this.messageDataArray[this.messageDataArray.length - 1];
      return title || '';
    } else {
      return '';
    }
  }

  /**
   * @description: 消息类型 error/warning/info
   * @return {String}
   */
  @computed get deviceMessageType() {
    if (this.messageDataArray.length > 0) {
      const {payloadData} =
        this.messageDataArray[this.messageDataArray.length - 1];
      const {eventType} = payloadData || {};
      return eventType || '';
    } else {
      return '';
    }
  }

  /**
   * @description: 设备id
   * @return {String}
   */
  @computed get deviceId() {
    const {deviceId} = this.initialParams;
    return deviceId;
  }

  /**
   * @description: 设备mac地址
   * @return {String}
   */
  @computed get mac() {
    const {mac} = this.initialParams;
    return mac;
  }
  /**
   * @description: 设备名字
   * @return {String}
   */
  @computed get deviceName() {
    let {deviceName} = this.initialParams;
    if (this.deviceDetail?.deviceName) {
      deviceName = this.deviceDetail?.deviceName;
    }
    // deviceName返回不正常
    return deviceName;
  }

  /**
   * @description: 产品id
   * @return {String}
   */
  @computed get productId() {
    let {productId} = this.initialParams;
    if (!productId || productId.length === 0) {
      productId = this.deviceDetail?.productId;
    }
    return productId;
  }

  /**
   * @description: 地区 NA/EU  EU:欧洲
   * @return {String}
   */
  @computed get region() {
    const {region} = this.initialParams;
    return region;
  }

  /**
   * @description: 电池详情-是否在仓数组
   * @return {Array}
   */
  @computed get haveBatteryArray() {
    const {batteryParseResult} = this;
    const {battery_pack} = batteryParseResult;
    // dpDataArray结构为[仓1是否存在,仓1百分比·······,仓6是否存在,仓6百分比]
    const dpDataArray = this.getArrayFromString(battery_pack);
    const result = [];
    dpDataArray.forEach((item, index) => {
      // 偶数位为仓位是否存在
      if (index % 2 === 0) {
        result.push(Boolean(Number(item)));
      }
    });
    return result;
  }

  /**
   * @description: 电池详情 value 数组
   * @return {Array}
   */
  @computed get valueArray() {
    const {batteryParseResult} = this;
    const {battery_pack} = batteryParseResult;
    // dpDataArray结构为[仓1是否存在,仓1百分比·······,仓6是否存在,仓6百分比]
    const dpDataArray = this.getArrayFromString(battery_pack);
    const result = [];
    dpDataArray.forEach((item, index) => {
      // 奇数位为电量百分比
      if (index % 2 !== 0) {
        const value = Number.parseInt(item, 16);
        result.push(value);
      }
    });
    return result;
  }

  /**
   * @description: 电池详情 是否在充电数组
   * @return {Array}
   */
  @computed get isChargingArray() {
    const {batteryParseResult} = this;
    const {battery_charge_status} = batteryParseResult;
    const dataHex = battery_charge_status;
    // dpDataArray结构为[仓1是否存在,仓1是否在充电·······,仓6是否存在,仓6是否在充电]
    const dpDataArray = this.getArrayFromString(dataHex);
    const result = [];
    dpDataArray.forEach((item, index) => {
      // 奇数位为是否在充电
      if (index % 2 !== 0) {
        // "1": "正在充电",
        // "0": "不在充电",
        const isCharging = Number(item) === 1;
        result.push(isCharging);
      }
    });
    return result;
  }

  /**
   * @description: 电池详情数据源
   * @return {Array}
   */
  @computed get batteryDetailArray() {
    if (this.haveBatteryArray.length < 6) {
      // 电池详情数据不全时，返回空数组
      return [];
    }
    const objArray = this.haveBatteryArray.map((haveFlag, index) => {
      const didHave = haveFlag; // 是否有电池
      const value = this.valueArray[index]; // 电量百分比
      const isError = false; // 是否有故障
      const isCharging = this.isChargingArray[index]; // 是否在充电
      return {didHave, value, isError, isCharging};
    });
    // 由电池详情数据源[1,2,3,4,5,6]转换成[1,2],[3,4],[5,6]格式 UI中采用三横排显示，每横排两个电池
    const result = this.defaultBatteryDetailData.map((item, index) => {
      item.first = {...objArray[index * 2]};
      item.second = {...objArray[index * 2 + 1]};
      return item;
    });
    return result;
  }

  /**
   * @description: 首页面板状态 开启&行驶中 充电 割草   // running\charging\mowing
   * @return {String}
   */
  @computed get currentStatus() {
    const {parseResult} = this;
    const {isCharging, isMowing} = parseResult;
    let result = '';
    if (isCharging === true) {
      result = CURRENT_STATUS.CHARGING;
    } else if (isMowing === true) {
      result = CURRENT_STATUS.MOWING;
    } else {
      result = CURRENT_STATUS.RUNNING;
    }
    return result;
  }

  /**
   * @description: 首页速度显示 带单位字符串 MPH
   * @return {String}
   */
  @computed get runningSpeed() {
    const {parseResult} = this;
    const {numCurrentSpeedGear} = parseResult;
    let numCurrentSpeedGearTemp = 0.0;
    if (numCurrentSpeedGear && numCurrentSpeedGear > 0) {
      numCurrentSpeedGearTemp = (numCurrentSpeedGear / 100).toFixed(2);
    }
    const result = `${numCurrentSpeedGearTemp} ${Strings.getLang(
      'rn_4913_panelhome_runningspeedunit_textview_text',
    )}`;
    return result;
  }

  /**
   * @description: 首页刀盘转速显示 带单位字符串 RPM
   * @return {String}
   */
  @computed get bladeRotation() {
    const {parseResult} = this;
    const {numCurrentBladeRpmGear} = parseResult;
    let result;
    if (numCurrentBladeRpmGear !== undefined) {
      result = `${numCurrentBladeRpmGear} ${Strings.getLang(
        'rn_4913_panelhome_bladerotationunit_textview_text',
      )}`;
    }
    return result;
  }

  /**
   * @description: 首页剩余充电时间 带单位字符串
   * @return {String}
   */
  @computed get remainChargingTime() {
    const {parseResult} = this;
    const {numTotalRemainChargeTime} = parseResult;
    const tmpArray = TimeUtils.parseSecond(numTotalRemainChargeTime * 60);
    const hour = tmpArray.length > 0 && tmpArray[0];
    const min = tmpArray.length > 1 && tmpArray[1];
    // 00 h 19 min
    const result = `${hour} ${Strings.getLang(
      'rn_common_unit_hour_textview_text',
    )} ${min} ${Strings.getLang('rn_common_unit_min_textview_text')}`;
    return result;
  }

  /**
   * @description: 首页剩余工作时间，带单位字符串
   * @return {String}
   */
  @computed get remainRunningTime() {
    const {parseResult} = this;
    const {numTotalRemainDisChargeTime} = parseResult;
    const tmpArray = TimeUtils.parseSecond(numTotalRemainDisChargeTime * 60);
    const hour = tmpArray.length > 0 && tmpArray[0];
    const min = tmpArray.length > 1 && tmpArray[1];
    const result = `${hour} ${Strings.getLang(
      'rn_common_unit_hour_textview_text',
    )} ${min} ${Strings.getLang('rn_common_unit_min_textview_text')}`;
    return result;
  }
  /**
   * @description: 蓝牙连接标识文案
   * @return {String}
   */
  @computed get connectDesc() {
    const result = this.bleConnected
      ? Strings.getLang('rn_4913_panelhome_connected_textview_text')
      : Strings.getLang('rn_4913_panelhome_disconnected_textview_text');
    return result;
  }

  /**
   * @description 是否展示锁定按钮
   * @return {Boalean}
   */
  @computed get isLock() {
    const {isLockDataSource} = this;
    return isLockDataSource;
  }

  /**
   * @description: home 倒车提醒状态
   *  0 为off 1为beep 2为灯光
   * @return {Number}
   */
  @computed get backUpAlertStatus() {
    const {backUpDataSource} = this;
    const {isSetBackLightOn, numLightBrightnessGear} = backUpDataSource;
    let result = 0;
    if (isSetBackLightOn === true) {
      if (numLightBrightnessGear > 0) {
        result = 1;
      } else {
        result = 2;
      }
    }
    return result;
  }

  /**
   * @description: home 日光灯状态
   * 0为off 1为跑马灯 2为灯光
   * @return {Number}
   */
  @computed get daytimeLightStatus() {
    const {dayLightDataSource} = this;
    const {isSetHorseLightOn, isSetDayLightOn} = dayLightDataSource;
    let result = 0;
    if (isSetDayLightOn === true) {
      if (isSetHorseLightOn === true) {
        result = 1;
      } else {
        result = 2;
      }
    }
    return result;
  }

  /**
   * @description: home 大灯延时关闭时间（这里是档位）
   * @return {Number}
   */
  @computed get lightDelayOffTimeLevel() {
    const {numberLightDelayOffLevel} = this;
    let result = numberLightDelayOffLevel || 0;
    return result;
  }

  /**
   * @description: 字符串中获取数组,每两个字符为一个item
   * @param {String} str
   * @return {Array} result
   */
  getArrayFromString = (str = '') => {
    let result = [];
    if (str.length < 24) {
      return result;
    }
    result = str.match(/.{1,2}/g);
    if (result.length > 12) {
      result = result.slice(0, 12);
    }
    return result;
  };
  /**
   * @description: 默认电池详情数据源
   * @return {Array}
   */
  defaultBatteryDetailData = [
    {
      key: 'fistLine',
      first: {
        didHave: false,
        isCharging: false,
        isError: false,
        value: 0,
        healthType: 3,
      },
      second: {
        didHave: false,
        isCharging: false,
        isError: false,
        value: 0,
        healthType: 3,
      },
    },
    {
      key: 'secondLine',
      first: {
        didHave: false,
        isCharging: false,
        isError: false,
        value: 0,
        healthType: 3,
      },
      second: {
        didHave: false,
        isCharging: false,
        isError: false,
        value: 0,
        healthType: 3,
      },
    },
    {
      key: 'thirdLine',
      first: {
        didHave: false,
        isCharging: false,
        isError: false,
        value: 0,
        healthType: 3,
      },
      second: {
        didHave: false,
        isCharging: false,
        isError: false,
        value: 0,
        healthType: 3,
      },
    },
  ];
}
