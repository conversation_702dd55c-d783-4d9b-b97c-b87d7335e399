/*
 * @Author: <EMAIL>
 * @Date: 2023-11-17 10:21:14
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2024-01-25 01:40:52
 * @FilePath: /Z6/src/mobx/index.js
 * @Description:
 *
 */
// store
import PanelStore from './store/panel';

// action
import PanelActions from './actions/panel';

// 全局store
export const panelModeStores = {
  panel: new PanelStore(),
};

export const panelModeActions = {
  panelActions: new PanelActions(panelModeStores),
};

export const mobxStates = {
  ...panelModeStores,
};

export const mobxActions = {
  ...panelModeActions,
};
