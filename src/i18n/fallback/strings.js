/*
 * @Author: <EMAIL>
 * @Date: 2023-11-17 10:21:14
 * @LastEditors: cdlsp
 * @FilePath: /chervonreactnative/Z6/src/i18n/strings.js
 * @Description: 国际化key-value
 *
 */
import commonStrings from './common';

export const newAddedMap = {
  en: {
    rn_4913_panelhome_connected_textview_text: 'Connected',
    rn_4913_panelhome_connecting_textview_text: 'Connecting',
    rn_4913_panelhome_notconnected_textview_text: 'Not Connected',
    rn_4913_panelhome_remaining_textview_text: 'Remaining Battery',
    rn_4913_panelhome_registration_textview_text: 'Registration Info',
    rn_4913_panelhome_registernow_textview_text: 'Register now',
    rn_4913_panelhome_lightdelay_textview_text: 'Light-off Delay(s)',
    rn_4913_panelhome_willturnoff_textview_text: 'Will turn off in ',
    rn_4913_batterydetail_title_textview_text: 'Battery details',
    rn_4913_batterydetail_charging_textview_text: 'Charing',
    rn_4913_panelhome_keybuttonalerttitle_textview_text:
      'Are you sure you want to turn it on?',
    rn_4913_panelhome_runningspeed_textview_text: 'Running Speed',
    rn_4913_panelhome_bladerotation_textview_text: 'Blade Rotation',
    rn_4913_panelhome_remainingchargingtime_textview_text:
      'Remaining Charging Time',
    rn_4913_panelhome_remainingruntime_textview_text: 'Remaining Runtime',
    rn_4913_panelhome_backupalert_textview_text: 'Back Up Alert',
    rn_4913_panelhome_daytimelight_textview_text: 'Daytime Light',
    rn_4913_panelhome_off_textview_text: 'OFF',
    rn_4913_panelhome_running_textview_text: 'RUNNING',
    rn_4913_panelhome_on_textview_text: 'ON',
    rn_4913_panelhome_beep_textview_text: 'BEEP',
    rn_4913_panelhome_light_textview_text: 'LIGHT',
    rn_4913_panelhome_runningspeedunit_textview_text: 'MPH',
    rn_4913_panelhome_bladerotationunit_textview_text: 'RPM',
    rn_4913_panelhome_disconnected_textview_text: 'Disconnected',
    rn_4913_panelhome_available_textview_text: 'available',
    rn_4913_panelhome_required_textview_text: 'maintenance required',
    rn_4913_panelhome_moreCard_textview_text: 'More to come...',
  },
};

export default {
  en: {
    ...commonStrings.en,
    ...newAddedMap.en,
  },
};
