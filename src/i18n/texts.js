import commonWords from './exported/common.json';
import otherWords from './exported/others.json';
import fallbackWords from './fallback/strings.js';

export default {
  en: fallbackWords.en,
  // for en in na
  'en-US': {
    ...commonWords['en-US'],
    ...otherWords['en-US'],
  },
  // for en in eu-ek
  'en-GB': {
    ...commonWords['en-GB'],
    ...otherWords['en-GB'],
  },
  // for other langs
  fr: {
    ...commonWords.fr,
    ...otherWords.fr,
  },
  no: {
    ...commonWords.no,
    ...otherWords.no,
  },
  de: {
    ...commonWords.de,
    ...otherWords.de,
  },
  se: {
    ...commonWords.se,
    ...otherWords.se,
  },
  fi: {
    ...commonWords.fi,
    ...otherWords.fi,
  },
  dk: {
    ...commonWords.dk,
    ...otherWords.dk,
  },
  it: {
    ...commonWords.it,
    ...otherWords.it,
  },
  nl: {
    ...commonWords.nl,
    ...otherWords.nl,
  },
  es: {
    ...commonWords.es,
    ...otherWords.es,
  },
};
