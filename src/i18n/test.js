/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-02-13 16:35:34
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2024-01-30 17:14:26
 * @FilePath: /Z6/src/i18n/test.js
 * @Description: 多语言制表格用
 */
import {newAddedMap} from 'IOTRN/src/i18n/strings';
export const getArrays = (array = []) => {
  getResultArrays(array);
};
export const getResultArrays = array => {
  const pageArray = [];
  const functionArray = [];
  const eleArray = [];
  const textArray = [];
  array.forEach((item, index) => {
    const tmpArray = item.split('_');
    if (tmpArray.length !== 6) {
      console.warn('格式不对,对应item是---item', item);
    }
    pageArray.push(`${tmpArray[1]}_${tmpArray[2]}`);
    functionArray.push(tmpArray[3]);
    eleArray.push(tmpArray[4]);
    textArray.push(tmpArray[5]);
  });
  const result = {
    pageArray,
    functionArray,
    eleArray,
    textArray,
  };
  console.log('result---getArrays--', JSON.stringify(result));
};
/**
 * 这个函数是为了打印出所有的多语言，用于制表格
 */
export const logLanguage = () => {
  const enObj = newAddedMap.en;
  const tmpKeys = [];
  const tmpValues = [];
  Object.keys(enObj).forEach(key => {
    tmpKeys.push(key);
    tmpValues.push(enObj[key]);
  });
  console.log('logLanguage---tmpKeys--', JSON.stringify(tmpKeys));

  console.log('logLanguage---tmpValues--', JSON.stringify(tmpValues));
  // 在这里使用 tmpKeys 或者进行其他操作
  getArrays(tmpKeys);
};
