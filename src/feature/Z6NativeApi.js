/*
 * @Author: <EMAIL>
 * @Date: 2023-11-17 10:21:14
 * @LastEditors: cdlsp
 * @LastEditTime: 2024-04-26 16:43:58
 * @FilePath: /chervonreactnative/Z6/src/feature/Z6NativeApi.js
 * @Description: 针对Z6实现的原生Module
 *
 */
import {NativeModules} from 'react-native';
const z6DeviceModule = NativeModules.RNCallNativeZ6Device;

/**
 * Z6发送命令
 */
const sendZ6Cmd = (cmdHex = '', mac = '') => {
  try {
    if (z6DeviceModule?.sendZ6Cmd) {
      z6DeviceModule.sendZ6Cmd(cmdHex, mac);
    }
  } catch (e) {
    console.log('error--', e);
  }
};

/**
 * 请求蓝牙连接
 */
const requestZ6BleConnect = (deviceId = '', mac = '') => {
  try {
    if (z6DeviceModule?.requestZ6BleConnect) {
      z6DeviceModule.requestZ6BleConnect(deviceId, mac);
    }
  } catch (e) {
    console.log('error--', e);
  }
};

/**
 * 原生待实现
 */
const isBleConnect = (mac = '') => {
  try {
    if (z6DeviceModule?.isBleConnect) {
      return z6DeviceModule.isBleConnect(mac);
    }
  } catch (e) {
    console.log('error--', e);
  }
};

/**
 * 安卓使用的轮询方法接口，开启安卓轮询
 * topic：蓝牙指令
 * mac：蓝牙mac地址
 */
const startPolling = (topic = '', mac = '', heartBeatTime = 3000) => {
  try {
    if (z6DeviceModule?.startPolling) {
      z6DeviceModule.startPolling(topic, mac, heartBeatTime);
    }
  } catch (e) {
    console.log('error--', e);
  }
};

/**
 * 安卓使用的轮询方法接口，停止安卓轮询
 */
const stopPolling = () => {
  try {
    if (z6DeviceModule?.stopPolling) {
      z6DeviceModule.stopPolling();
    }
  } catch (e) {
    console.log('error--', e);
  }
};

const z6Device = {
  ...z6DeviceModule,
  sendZ6Cmd,
  isBleConnect,
  requestZ6BleConnect,
  startPolling,
  stopPolling,
};

export {z6Device};
