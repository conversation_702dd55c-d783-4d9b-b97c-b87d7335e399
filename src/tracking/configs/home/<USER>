/**
 * return button click
 */
const PANEL_HOME_RETURN_BUTTON_CLICK = {
  id: '1',
  type: 'click',
  name: 'Return_Button_Click',
};

/**
 * device name click
 */
const PANEL_HOME_DEVICE_NAME_CLICK = {
  id: '2',
  type: 'click',
  name: 'Device_Name_Click',
};

/**
 * device details button click
 */
const PANEL_HOME_DEVICE_DETAILS_BUTTON_CLICK = {
  id: '3',
  type: 'click',
  name: 'Device_Details_Button_Click',
};

/**
 * key switch button click
 */
const PANEL_HOME_KEY_SWITCH_BUTTON_CLICK = {
  id: '4',
  type: 'click',
  name: 'Key_Switch_Button_Click',
};

/**
 * fault message button click
 */
const PANEL_HOME_FAULT_MESSAGE_BUTTON_CLICK = {
  id: '5',
  type: 'click',
  name: 'Fault_Message_Button_Click',
};

/**
 * battery pack expansion button click
 */
const PANEL_HOME_BATTERY_PACK_EXPANSION_BUTTON_CLICK = {
  id: '6',
  type: 'click',
  name: 'Battery_Pack_Expansion_Button_Click',
};

/**
 * accessories button click
 */
const PANEL_HOME_ACCESSORIES_BUTTON_CLICK = {
  id: '7',
  type: 'click',
  name: 'Accessories_Button_Click',
};

/**
 * device registration button click
 */
const PANEL_HOME_DEVICE_REGISTRATION_BUTTON_CLICK = {
  id: '8',
  type: 'click',
  name: 'Device_Registration_Button_Click',
};

/**
 * daytime light button click
 */
const PANEL_HOME_DAYTIME_LIGHT_BUTTON_CLICK = {
  id: '9',
  type: 'click',
  name: 'Daytime_Light_Button_Click',
};

/**
 * headlight delay off button click
 */
const PANEL_HOME_HEADLIGHT_DELAY_OFF_BUTTON_CLICK = {
  id: '10',
  type: 'click',
  name: 'Headlight_Delay_Off_Button_Click',
};

/**
 * reverse reminder button click
 */
const PANEL_HOME_REVERSE_REMINDER_BUTTON_CLICK = {
  id: '11',
  type: 'click',
  name: 'Reverse_Reminder_Button_Click',
};

export const PANEL_HOME_PAGE_ELEMENTS_MAP = {
  /**
   * return button click
   */
  returnButtonClick: PANEL_HOME_RETURN_BUTTON_CLICK,
  /**
   * device name click
   */
  deviceNameClick: PANEL_HOME_DEVICE_NAME_CLICK,
  /**
   * device details button click
   */
  deviceDetailsButtonClick: PANEL_HOME_DEVICE_DETAILS_BUTTON_CLICK,

  /**
   * key switch button click
   */
  keySwitchButtonClick: PANEL_HOME_KEY_SWITCH_BUTTON_CLICK,
  /**
   * fault message button click
   */
  faultMessageButtonClick: PANEL_HOME_FAULT_MESSAGE_BUTTON_CLICK,
  /**
   * battery pack expansion button click
   */
  batteryPackExpansionButtonClick:
    PANEL_HOME_BATTERY_PACK_EXPANSION_BUTTON_CLICK,

  /**
   * accessories button click
   */
  accessoriesButtonClick: PANEL_HOME_ACCESSORIES_BUTTON_CLICK,

  /**
   * device registration button click
   */
  deviceRegistrationButtonClick: PANEL_HOME_DEVICE_REGISTRATION_BUTTON_CLICK,

  /**
   * daytime light button click
   */
  daytimeLightButtonClick: PANEL_HOME_DAYTIME_LIGHT_BUTTON_CLICK,
  /**
   * headlight delay off button click
   */
  headlightDelayOffButtonClick: PANEL_HOME_HEADLIGHT_DELAY_OFF_BUTTON_CLICK,
  /**
   * reverse reminder button click
   */
  reverseReminderButtonClick: PANEL_HOME_REVERSE_REMINDER_BUTTON_CLICK,
};

export default {
  id: '258',
  name: 'ViewPanelHome',
  blocks: [
    {
      id: '0',
      elements: [
        {
          id: '0',
          type: 'exposure',
          name: 'Home_Page_Exposure',
        },
        PANEL_HOME_RETURN_BUTTON_CLICK,
        PANEL_HOME_DEVICE_NAME_CLICK,
        PANEL_HOME_DEVICE_DETAILS_BUTTON_CLICK,
        PANEL_HOME_KEY_SWITCH_BUTTON_CLICK,
        PANEL_HOME_FAULT_MESSAGE_BUTTON_CLICK,
        PANEL_HOME_BATTERY_PACK_EXPANSION_BUTTON_CLICK,
        PANEL_HOME_ACCESSORIES_BUTTON_CLICK,
        PANEL_HOME_DEVICE_REGISTRATION_BUTTON_CLICK,
        PANEL_HOME_DAYTIME_LIGHT_BUTTON_CLICK,
        PANEL_HOME_HEADLIGHT_DELAY_OFF_BUTTON_CLICK,
        PANEL_HOME_REVERSE_REMINDER_BUTTON_CLICK,
        {
          id: '12',
          type: 'duration',
          name: 'Home_Page_Stay_Duration',
        },
      ],
    },
  ],
};
