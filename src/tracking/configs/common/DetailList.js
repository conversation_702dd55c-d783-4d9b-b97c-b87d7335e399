/**
 * return button click
 */
const DETAIL_LIST_RETURN_BUTTON_CLICK = {
  id: '1',
  type: 'click',
  name: 'Return_Button_Click',
};

/**
 * equipment name button click
 */
const DETAIL_LIST_EQUIPMENT_NAME_BUTTON_CLICK = {
  id: '2',
  type: 'click',
  name: 'Equipment_Name_Button_Click',
};

/**
 * device registration button click
 */
const DETAIL_LIST_DEVICE_REGISTRATION_BUTTON_CLICK = {
  id: '3',
  type: 'click',
  name: 'Device_Registration_Button_Click',
};

/**
 * Product Encyclopedia Button Click
 */
const DETAIL_LIST_PRODUCT_ENCYCLOPEDIA_BUTTON_CLICK = {
  id: '4',
  type: 'click',
  name: 'Product_Encyclopedia_Button_Click',
};

/**
 * Equipent Information Button Click
 */
const DETAIL_LIST_EQUIPENT_INFORMATION_BUTTON_CLICK = {
  id: '5',
  type: 'click',
  name: 'Equipent_Information_Button_Click',
};

/**
 * Enclosure Button Click
 */
const DETAIL_LIST_ENCLOSURE_BUTTON_CLICK = {
  id: '6',
  type: 'click',
  name: 'Enclosure_Button_Click',
};

/**
 * Delete Device Button Click
 */
const DETAIL_LIST_DELETE_DEVICE_BUTTON_CLICK = {
  id: '7',
  type: 'click',
  name: 'Delete_Device_Button_Click',
};

/**
 * Device Notification Button Click
 */
const DETAIL_LIST_DEVICE_NOTIFICATION_BUTTON_CLICK = {
  id: '8',
  type: 'click',
  name: 'Device_Notification_Button_Click',
};

/**
 * Feedback Button Click
 */
const DETAIL_LIST_FEEDBACK_BUTTON_CLICK = {
  id: '9',
  type: 'click',
  name: 'Feedback_Button_Click',
};

export const DETAIL_LIST_PAGE_ELEMENTS_MAP = {
  /**
   * return button click
   */
  returnButtonClick: DETAIL_LIST_RETURN_BUTTON_CLICK,
  /**
   * equipment name button click
   */
  equipmentNameButtonClick: DETAIL_LIST_EQUIPMENT_NAME_BUTTON_CLICK,
  /**
   * device registration button click
   */
  deviceRegistrationButtonClick: DETAIL_LIST_DEVICE_REGISTRATION_BUTTON_CLICK,
  /**
   * product encyclopedia button click
   */
  productEncyclopediaButtonClick: DETAIL_LIST_PRODUCT_ENCYCLOPEDIA_BUTTON_CLICK,
  /**
   * equipent information button click
   */
  equipentInformationButtonClick: DETAIL_LIST_EQUIPENT_INFORMATION_BUTTON_CLICK,
  /**
   * enclosure button click
   */
  enclosureButtonClick: DETAIL_LIST_ENCLOSURE_BUTTON_CLICK,
  /**
   * delete device button click
   */
  deleteDeviceButtonClick: DETAIL_LIST_DELETE_DEVICE_BUTTON_CLICK,
  /**
   * device notification button click
   */
  deviceNotificationButtonClick: DETAIL_LIST_DEVICE_NOTIFICATION_BUTTON_CLICK,
  /**
   * feedback button click
   */
  feedbackButtonClick: DETAIL_LIST_FEEDBACK_BUTTON_CLICK,
};

export default {
  id: '260',
  name: 'ViewDetailList',
  blocks: [
    {
      id: '0',
      elements: [
        {
          id: '0',
          type: 'exposure',
          name: 'Device_Details_Page_Exposure',
        },
        DETAIL_LIST_RETURN_BUTTON_CLICK,
        DETAIL_LIST_EQUIPMENT_NAME_BUTTON_CLICK,
        DETAIL_LIST_DEVICE_REGISTRATION_BUTTON_CLICK,
        DETAIL_LIST_PRODUCT_ENCYCLOPEDIA_BUTTON_CLICK,
        DETAIL_LIST_EQUIPENT_INFORMATION_BUTTON_CLICK,
        DETAIL_LIST_ENCLOSURE_BUTTON_CLICK,
        DETAIL_LIST_DELETE_DEVICE_BUTTON_CLICK,
        DETAIL_LIST_DEVICE_NOTIFICATION_BUTTON_CLICK,
        DETAIL_LIST_FEEDBACK_BUTTON_CLICK,
        {
          id: '10',
          type: 'duration',
          name: 'Device_Details_Page_Stay_Duration',
        },
      ],
    },
  ],
};
