{
  "presets": [
    "module:metro-react-native-babel-preset"
  ],
  "plugins": [
    [
      "@babel/plugin-proposal-decorators",
      {
        "legacy": true
      }
    ],
    ["module-resolver", {
      "root": ["./"],
      "alias": {
        "@tracer": "./src/utils/tracer",
        "@api": "./src/api",
        "@components": "./src/components",
        "@config": "./src/config",
        "@i18n": "./src/i18n",
        "@tracking": "./src/tracking",
        "@assets": "./src/assets",
        "@utils": "./src/utils",
        "@pages": "./src/pages",
        "IOTRN": "./",
        "@cvn-icon": "./src/utils/cvn-icon",
        "@feature": "./src/feature"
      }
    }],
    // ["transform-remove-console", { "exclude": [ "error", "warn"] }]
  ],
  "env": {
    "production": {
      "plugins": [
        // "transform-remove-console"
        ["transform-remove-console", { "exclude": [ "error", "warn"] }]
      ]
    }
  }
}