/*
 * @Author: 邢立伟 <EMAIL>
 * @Date: 2023-03-23 10:53:09
 * @LastEditors: 邢立伟 <EMAIL>
 * @LastEditTime: 2023-06-06 14:58:41
 * @FilePath: /Tractor/App.js
 * @Description: app入口
 */
import React from 'react';
import {Provider as MobxProvider} from 'mobx-react/native';

import {NavigationContainer} from '@react-navigation/native';
import HomeStackScreen from '@pages/stacks/HomeStackScreen';
import {mobxActions, mobxStates} from 'src/mobx/index.js';
import Strings from '@i18n';
import {LogBox} from 'react-native';
import {routeListener} from '@utils/log';

export default class App extends React.Component {
  constructor(props) {
    super(props);

    LogBox.ignoreLogs([
      'Non-serializable values were found in the navigation state',
    ]);

    this.setLanguageBasedOnProps();
  }

  setLanguageBasedOnProps = () => {
    const {lang = 'en', region = 'NA'} = this.props;

    if (region === 'NA' && lang === 'en') {
      Strings.setLanguage('en-US');
    } else if (region === 'EU' && lang === 'en') {
      Strings.setLanguage('en-GB');
    } else {
      Strings.setLanguage(lang);
    }
  };

  render() {
    return (
      <MobxProvider {...mobxStates} {...mobxActions}>
        <TabRootNav {...this.props} />
      </MobxProvider>
    );
  }
}

const TabRootNav = ({...props}) => {
  return (
    <NavigationContainer
      onStateChange={res => {
        routeListener(res);
      }}>
      <HomeStackScreen {...props} />
    </NavigationContainer>
  );
};
// 重写console.log 空函数
if (!__DEV__) {
  console.log = function () {};
  console.warn = function () {};
  console.table = function () {};
}
