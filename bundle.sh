#! /bin/bash
###
 # @Author: cdlsp
 # @Date: 2024-07-11 13:59:28
 # @LastEditors: <EMAIL>
 # @LastEditTime: 2024-07-12 10:23:54
 # @FilePath: /Z6/bundle.sh
 # @Description: 
 # 
### 

bundle_name="4913_ZT4200L_ridingMowers"
bundle_name_ios="${bundle_name}.ios.bundle"
bundle_name_android="${bundle_name}.android.bundle"
zip_name_ios="${bundle_name}.ios.zip"
zip_name_android="${bundle_name}.android.zip"

WORKSPACE=$(pwd)

# 当前时间
begin_time=$(date +%Y-%m-%d-%H-%M-%S)
echo "******** begin build ${begin_time} *****"
#出包路径, 最后包会复制到此目录
archive_path="${WORKSPACE}/archive/${begin_time}"

# build ios
echo "********* build ios and android bundle ********"
npx mcs-scripts build -t busine -e index.js -od ${archive_path} -bn ${bundle_name}

# 压缩包
echo "********* zip ios and android bundle ********"

cd ${archive_path}

#iOS
zip ${zip_name_ios} -r ${bundle_name_ios} assets

#Android
zip ${zip_name_android} -r ${bundle_name_android} drawable-*

# copy bundle zips to bundle_zips
echo "********* copy bundle zips to bundle_zips ********"
# 创建 zip 文件夹
bundle_zips_folder="${archive_path}/bundle_zips"
mkdir -p  ${bundle_zips_folder}
mv ${zip_name_ios}  ${zip_name_android} ${bundle_zips_folder}

echo "done"
